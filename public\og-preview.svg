<svg width="1200" height="630" viewBox="0 0 1200 630" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0f172a;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#1e3a8a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0f172a;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#06b6d4;stop-opacity:1" />
    </linearGradient>
    <filter id="glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="1200" height="630" fill="url(#bgGradient)"/>
  
  <!-- Grid Pattern -->
  <defs>
    <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
      <path d="M 40 0 L 0 0 0 40" fill="none" stroke="#1e40af" stroke-width="0.5" opacity="0.1"/>
    </pattern>
  </defs>
  <rect width="1200" height="630" fill="url(#grid)"/>
  
  <!-- Logo Background Circle -->
  <circle cx="180" cy="200" r="60" fill="url(#logoGradient)" opacity="0.2"/>
  
  <!-- OTX Logo -->
  <rect x="150" y="170" width="60" height="60" rx="12" fill="url(#logoGradient)" filter="url(#glow)"/>
  <text x="180" y="210" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="28" font-weight="bold" fill="white">OTX</text>
  
  <!-- Main Title -->
  <text x="280" y="160" font-family="Inter, system-ui, sans-serif" font-size="48" font-weight="bold" fill="white">
    OTX Platform
  </text>
  
  <!-- Subtitle -->
  <text x="280" y="200" font-family="Inter, system-ui, sans-serif" font-size="32" font-weight="600" fill="#60a5fa">
    Complete Trading Infrastructure
  </text>
  
  <!-- Tagline -->
  <text x="280" y="240" font-family="Inter, system-ui, sans-serif" font-size="20" fill="#94a3b8">
    Everything you need to start and scale your brokerage business
  </text>
  
  <!-- Feature Pills -->
  <g transform="translate(280, 280)">
    <!-- CRM System -->
    <rect x="0" y="0" width="120" height="32" rx="16" fill="#1e40af" opacity="0.3"/>
    <text x="60" y="22" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="14" font-weight="500" fill="#60a5fa">CRM System</text>
    
    <!-- Mobile App -->
    <rect x="140" y="0" width="120" height="32" rx="16" fill="#059669" opacity="0.3"/>
    <text x="200" y="22" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="14" font-weight="500" fill="#34d399">Mobile App</text>
    
    <!-- Dealer Terminal -->
    <rect x="280" y="0" width="140" height="32" rx="16" fill="#7c3aed" opacity="0.3"/>
    <text x="350" y="22" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="14" font-weight="500" fill="#a78bfa">Dealer Terminal</text>
  </g>
  
  <!-- Call to Action -->
  <g transform="translate(280, 360)">
    <rect x="0" y="0" width="200" height="50" rx="25" fill="url(#logoGradient)" filter="url(#glow)"/>
    <text x="100" y="35" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="18" font-weight="600" fill="white">Launch Your Brokerage</text>
  </g>
  
  <!-- Trading Terminal Mockup -->
  <g transform="translate(700, 80)">
    <!-- Terminal Window -->
    <rect x="0" y="0" width="450" height="280" rx="12" fill="#1e293b" stroke="#475569" stroke-width="2" opacity="0.9"/>
    
    <!-- Window Header -->
    <rect x="0" y="0" width="450" height="40" rx="12" fill="#334155"/>
    <circle cx="20" cy="20" r="6" fill="#ef4444"/>
    <circle cx="40" cy="20" r="6" fill="#f59e0b"/>
    <circle cx="60" cy="20" r="6" fill="#10b981"/>
    <text x="225" y="28" text-anchor="middle" font-family="Inter, system-ui, sans-serif" font-size="12" font-weight="500" fill="#94a3b8">OTX Dealer Terminal</text>
    
    <!-- Trading Data Rows -->
    <g transform="translate(20, 60)">
      <!-- Header -->
      <text x="0" y="20" font-family="Inter, system-ui, sans-serif" font-size="12" font-weight="600" fill="#60a5fa">Symbol</text>
      <text x="80" y="20" font-family="Inter, system-ui, sans-serif" font-size="12" font-weight="600" fill="#60a5fa">Bid</text>
      <text x="140" y="20" font-family="Inter, system-ui, sans-serif" font-size="12" font-weight="600" fill="#60a5fa">Ask</text>
      <text x="200" y="20" font-family="Inter, system-ui, sans-serif" font-size="12" font-weight="600" fill="#60a5fa">Change</text>
      <text x="280" y="20" font-family="Inter, system-ui, sans-serif" font-size="12" font-weight="600" fill="#60a5fa">Volume</text>
      
      <!-- Data Rows -->
      <text x="0" y="45" font-family="JetBrains Mono, monospace" font-size="11" fill="#e2e8f0">EUR/USD</text>
      <text x="80" y="45" font-family="JetBrains Mono, monospace" font-size="11" fill="#e2e8f0">1.0856</text>
      <text x="140" y="45" font-family="JetBrains Mono, monospace" font-size="11" fill="#e2e8f0">1.0858</text>
      <text x="200" y="45" font-family="JetBrains Mono, monospace" font-size="11" fill="#10b981">+0.0012</text>
      <text x="280" y="45" font-family="JetBrains Mono, monospace" font-size="11" fill="#64748b">2.4M</text>
      
      <text x="0" y="65" font-family="JetBrains Mono, monospace" font-size="11" fill="#e2e8f0">GBP/USD</text>
      <text x="80" y="65" font-family="JetBrains Mono, monospace" font-size="11" fill="#e2e8f0">1.2643</text>
      <text x="140" y="65" font-family="JetBrains Mono, monospace" font-size="11" fill="#e2e8f0">1.2645</text>
      <text x="200" y="65" font-family="JetBrains Mono, monospace" font-size="11" fill="#ef4444">-0.0008</text>
      <text x="280" y="65" font-family="JetBrains Mono, monospace" font-size="11" fill="#64748b">1.8M</text>
      
      <text x="0" y="85" font-family="JetBrains Mono, monospace" font-size="11" fill="#e2e8f0">USD/JPY</text>
      <text x="80" y="85" font-family="JetBrains Mono, monospace" font-size="11" fill="#e2e8f0">150.24</text>
      <text x="140" y="85" font-family="JetBrains Mono, monospace" font-size="11" fill="#e2e8f0">150.26</text>
      <text x="200" y="85" font-family="JetBrains Mono, monospace" font-size="11" fill="#10b981">+0.15</text>
      <text x="280" y="85" font-family="JetBrains Mono, monospace" font-size="11" fill="#64748b">3.1M</text>
      
      <text x="0" y="105" font-family="JetBrains Mono, monospace" font-size="11" fill="#e2e8f0">XAU/USD</text>
      <text x="80" y="105" font-family="JetBrains Mono, monospace" font-size="11" fill="#e2e8f0">1987.45</text>
      <text x="140" y="105" font-family="JetBrains Mono, monospace" font-size="11" fill="#e2e8f0">1987.65</text>
      <text x="200" y="105" font-family="JetBrains Mono, monospace" font-size="11" fill="#10b981">+12.30</text>
      <text x="280" y="105" font-family="JetBrains Mono, monospace" font-size="11" fill="#64748b">856K</text>
    </g>
    
    <!-- Chart Area Simulation -->
    <g transform="translate(20, 180)">
      <rect x="0" y="0" width="410" height="80" fill="#0f1419" rx="4" opacity="0.5"/>
      <polyline points="10,60 50,45 90,35 130,50 170,25 210,40 250,30 290,20 330,35 370,25 400,15" 
                fill="none" stroke="#10b981" stroke-width="2" opacity="0.8"/>
      <text x="5" y="75" font-family="Inter, system-ui, sans-serif" font-size="10" fill="#64748b">Real-time Price Chart</text>
    </g>
  </g>
  
  <!-- Bottom Info -->
  <text x="100" y="580" font-family="Inter, system-ui, sans-serif" font-size="16" font-weight="500" fill="#64748b">
    Professional Trading Infrastructure • CRM • Mobile Apps • Dealer Terminal
  </text>
  
  <!-- Website URL -->
  <text x="1100" y="580" text-anchor="end" font-family="Inter, system-ui, sans-serif" font-size="14" font-weight="500" fill="#60a5fa">
    otx-dealer-terminal.vercel.app
  </text>
</svg>