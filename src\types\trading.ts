// Trading-related types for the nexus-trader-terminal application

// New interface types for reference image layout
export interface CurrencyPair {
  symbol: string;
  name: string;
  bid: number;
  ask: number;
  change: number;
  changePercent: number;
}

export interface ChartDataPoint {
  time: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

export interface Message {
  id: string;
  type: 'filled' | 'canceled' | 'rejected' | 'info' | 'pending';  
  content: string;
  timestamp: string;
  orderId?: string;
}

export interface AccountSummary {
  balance: number;
  equity: number;
  fpl: number;
  usedMargin: number;
  usableMargin: number;
  account: string;
  currency: string;
  user: string;
}

export type Timeframe = '1m' | '5m' | '15m' | '1H' | '4H' | '1D';

// Existing interfaces - maintained for compatibility

export interface Position {
  positionId: string;
  symbol: string;
  type: 'BUY' | 'SELL';
  volume: number;
  openPrice: number;
  currentPrice: number;
  sl?: number; // Stop Loss
  tp?: number; // Take Profit
  pl: number; // Profit/Loss
  openTime: string;
  swap: number;
  commission: number;
  comment?: string;
}

export interface Order {
  orderId: string;
  symbol: string;
  type: string;
  volume: number;
  orderPrice: number;
  sl?: number;
  tp?: number;
  currentPrice: string;
  state: string;
  placementTime: string;
  expiration?: string;
  comment?: string;
}

export interface Quote {
  symbol: string;
  bid: number;
  ask: number;
  spread: number;
  dailyChange: number;
  time: string;
}

export interface TraderAccount {
  accountId: string;
  balance: number;
  floatingPnL: number;
  equity: number;
  currency: string;
  usedMargin: number;
  marginLevel: number;
  marginCall: number;
  stopOut: number;
}

export interface Deal {
  executionTime: string;
  dealId: string;
  symbol?: string;
  type: 'balance' | 'buy' | 'sell';
  direction?: 'in' | 'out';
  volume?: number;
  executionPrice?: number;
  orderId?: string;
  commission: number;
  swap: number;
  profitLoss: number;
  balance: number;
  comment?: string;
}

export interface ActivityEntry {
  id: string;
  timestamp: string;
  action: 'CREATE' | 'EDIT' | 'DELETE' | 'CLOSE' | 'CANCEL' | 'LOGIN' | 'LOGOUT';
  entityType: 'Position' | 'Order' | 'Deal' | 'Account' | 'System';
  entityId: string;
  details: string;
  symbol?: string;
  changes?: { field: string; from: any; to: any }[];
}

// Chart data interfaces for KLineChart integration
export interface KLineData {
  timestamp: number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

export interface ChartConfig {
  symbol: string;
  timeframe: string;
  indicators: string[];
  overlays: string[];
}

export interface TimeframeOption {
  multiplier: number;
  timespan: 'minute' | 'hour' | 'day';
  text: string;
}

export interface IndicatorConfig {
  name: string;
  enabled: boolean;
  parameters?: any;
}

export interface OverlayConfig {
  type: string;
  enabled: boolean;
  coordinates?: any;
}

export interface HistoryRecord {
  id: string;
  type: 'position' | 'order' | 'deal';
  symbol: string;
  volume: number;
  openPrice?: number;
  closePrice?: number;
  openTime: string;
  closeTime?: string;
  profit?: number;
  status: string;
  comment?: string;
}

// Context menu types
export interface ContextMenuState {
  x: number;
  y: number;
  position: Position;
}

export interface ContextMenuAction {
  label: string;
  action: () => void;
  destructive?: boolean;
  icon?: React.ReactNode;
}

// Filter types (simplified for single account)
export interface PositionFilters {
  symbol: string;
  type?: 'BUY' | 'SELL';
}

export interface OrderFilters {
  symbol: string;
  orderType: string;
  state?: string;
}

// Error handling types
export interface ApiError {
  code: string;
  message: string;
  details?: string;
}

export interface ValidationError {
  field: string;
  message: string;
}

// Modal props types
export interface BaseModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export interface PositionModalProps extends BaseModalProps {
  position?: Position;
}

export interface OrderModalProps extends BaseModalProps {
  order?: Order;
}

export interface ChartModalProps extends BaseModalProps {
  symbol: string;
  chartData: KLineData[];
}