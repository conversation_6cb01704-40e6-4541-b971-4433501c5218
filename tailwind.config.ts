import type { Config } from "tailwindcss";

export default {
	darkMode: ["class"],
	content: [
		"./pages/**/*.{ts,tsx}",
		"./components/**/*.{ts,tsx}",
		"./app/**/*.{ts,tsx}",
		"./src/**/*.{ts,tsx}",
	],
	prefix: "",
	theme: {
		container: {
			center: true,
			padding: '2rem',
			screens: {
				'2xl': '1400px'
			}
		},
		extend: {
			fontFamily: {
				'sans': ['Inter', 'system-ui', 'sans-serif'],
				'mono': ['JetBrains Mono', 'SF Mono', 'Monaco', 'monospace'],
			},
			fontSize: {
				'xs': ['11px', { lineHeight: '1.4' }],
				'sm': ['12px', { lineHeight: '1.4' }],
				'base': ['14px', { lineHeight: '1.5' }],
				'lg': ['16px', { lineHeight: '1.5' }],
				'xl': ['18px', { lineHeight: '1.4' }],
				'2xl': ['24px', { lineHeight: '1.3' }],
			},
			colors: {
				border: 'hsl(var(--border))',
				input: 'hsl(var(--input))',
				ring: 'hsl(var(--ring))',
				background: 'hsl(var(--background))',
				foreground: 'hsl(var(--foreground))',
				primary: {
					DEFAULT: 'hsl(var(--primary))',
					foreground: 'hsl(var(--primary-foreground))'
				},
				secondary: {
					DEFAULT: 'hsl(var(--secondary))',
					foreground: 'hsl(var(--secondary-foreground))'
				},
				destructive: {
					DEFAULT: 'hsl(var(--destructive))',
					foreground: 'hsl(var(--destructive-foreground))'
				},
				muted: {
					DEFAULT: 'hsl(var(--muted))',
					foreground: 'hsl(var(--muted-foreground))'
				},
				accent: {
					DEFAULT: 'hsl(var(--accent))',
					foreground: 'hsl(var(--accent-foreground))'
				},
				popover: {
					DEFAULT: 'hsl(var(--popover))',
					foreground: 'hsl(var(--popover-foreground))'
				},
				card: {
					DEFAULT: 'hsl(var(--card))',
					foreground: 'hsl(var(--card-foreground))'
				},
				sidebar: {
					DEFAULT: 'hsl(var(--sidebar-background))',
					foreground: 'hsl(var(--sidebar-foreground))',
					primary: 'hsl(var(--sidebar-primary))',
					'primary-foreground': 'hsl(var(--sidebar-primary-foreground))',
					accent: 'hsl(var(--sidebar-accent))',
					'accent-foreground': 'hsl(var(--sidebar-accent-foreground))',
					border: 'hsl(var(--sidebar-border))',
					ring: 'hsl(var(--sidebar-ring))'
				},
				trading: {
					'bg-dark': '#0a0a0a',
					'bg-darker': '#050505',
					'panel': '#1a1a1a',
					'border': '#333333',
					'border-light': '#404040',
					'text': '#ffffff',
					'text-muted': '#a3a3a3',
					'text-dim': '#737373',
					'up': '#00ff88',
					'up-bg': '#00ff8810',
					'down': '#ff4757',
					'down-bg': '#ff475710',
					'accent': '#3b82f6',
					'accent-bg': '#3b82f610',
				}
			},
			borderRadius: {
				lg: 'var(--radius)',
				md: 'calc(var(--radius) - 2px)',
				sm: 'calc(var(--radius) - 4px)',
				'trading': '4px',
			},
			spacing: {
				'trading': '8px',
				'trading-sm': '6px',
				'trading-lg': '12px',
			},
			boxShadow: {
				'trading': '0 2px 8px rgba(0, 0, 0, 0.3)',
				'trading-lg': '0 4px 16px rgba(0, 0, 0, 0.4)',
				'trading-inner': 'inset 0 1px 3px rgba(0, 0, 0, 0.2)',
			},
			keyframes: {
				'accordion-down': {
					from: {
						height: '0'
					},
					to: {
						height: 'var(--radix-accordion-content-height)'
					}
				},
				'accordion-up': {
					from: {
						height: 'var(--radix-accordion-content-height)'
					},
					to: {
						height: '0'
					}
				}
			},
			animation: {
				'accordion-down': 'accordion-down 0.2s ease-out',
				'accordion-up': 'accordion-up 0.2s ease-out'
			}
		}
	},
	plugins: [require("tailwindcss-animate")],
} satisfies Config;
