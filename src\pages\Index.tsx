import React, { useEffect, useState } from 'react';
import { TradingProvider } from '../contexts/TradingContext';
import { TradingHeader } from '../components/TradingHeader';
import { MiniChart } from '../components/MiniChart';
import { Watchlist } from '../components/Watchlist';
import { TradePanel } from '../components/TradePanel';
import { WalletPanel } from '../components/WalletPanel';
import { MainChart } from '../components/MainChart';
import { BottomPanel } from '../components/BottomPanel';

const Index = () => {
  const [isDarkMode, setIsDarkMode] = useState(true);

  const toggleTheme = () => {
    setIsDarkMode(!isDarkMode);
  };

  useEffect(() => {
    document.documentElement.classList.toggle('dark', isDarkMode);
  }, [isDarkMode]);

  return (
    <TradingProvider>
      <div className="h-screen bg-trading-bg-dark font-sans text-trading-text flex flex-col overflow-hidden">
        {/* Header */}
        <TradingHeader isDarkMode={isDarkMode} toggleTheme={toggleTheme} />
        
        {/* Main Content Area - Grid Layout */}
        <div className="flex-1 grid grid-cols-[280px_1fr_300px] grid-rows-[1fr_200px] gap-0 overflow-hidden">
          {/* Left Column - Top Row */}
          <div className="flex flex-col">
            <MiniChart />
            <Watchlist />
          </div>
          
          {/* Center Column - Top Row (Main Chart) */}
          <div className="row-span-1">
            <MainChart />
          </div>
          
          {/* Right Column - Full Height (spans both rows) */}
          <div className="row-span-2 flex flex-col">
            {/* Trade Panel - 60% */}
            <div className="flex-[6]">
              <TradePanel />
            </div>
            {/* Wallet Panel - 40% */}
            <div className="flex-[4]">
              <WalletPanel />
            </div>
          </div>

          {/* Bottom Panel - Spans Left + Center columns */}
          <div className="col-span-2">
            <BottomPanel />
          </div>
        </div>
        
      </div>
    </TradingProvider>
  );
};

export default Index;