@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. */

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 217.2 91.2% 59.8%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    /* Trading color scheme */
    --trading-bg: 210 20% 96%;
    --trading-panel: 0 0% 100%;
    --trading-border: 214.3 31.8% 85.4%;
    --trading-text: 222.2 84% 4.9%;
    --trading-text-secondary: 215.4 16.3% 46.9%;
    
    /* Market colors */
    --bull: 142 76% 36%;
    --bear: 0 84% 60%;
    
    /* Chart colors */
    --chart-bg: 210 20% 98%;
    --chart-grid: 214.3 31.8% 91.4%;
    --chart-up: 142 76% 36%;
    --chart-down: 0 84% 60%;
    --chart-volume: 217.2 91.2% 59.8%;
    --chart-axis: 215.4 16.3% 46.9%;
    
    /* Bollinger Bands */
    --bollinger-upper: 25 95% 53%;
    --bollinger-mid: 217.2 91.2% 59.8%;
    --bollinger-lower: 330 81% 60%;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 91.2% 59.8%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    
    /* Dark trading color scheme */
    --trading-bg: 222.2 84% 4.9%;
    --trading-panel: 217.2 32.6% 17.5%;
    --trading-border: 217.2 32.6% 25.5%;
    --trading-text: 210 40% 98%;
    --trading-text-secondary: 215 20.2% 65.1%;
    
    /* Market colors (same) */
    --bull: 142 76% 36%;
    --bear: 0 84% 60%;
    
    /* Chart colors (dark) */
    --chart-bg: 222.2 84% 4.9%;
    --chart-grid: 217.2 32.6% 25.5%;
    --chart-up: 142 76% 36%;
    --chart-down: 0 84% 60%;
    --chart-volume: 217.2 91.2% 59.8%;
    --chart-axis: 215 20.2% 65.1%;
    
    /* Bollinger Bands (same) */
    --bollinger-upper: 25 95% 53%;
    --bollinger-mid: 217.2 91.2% 59.8%;
    --bollinger-lower: 330 81% 60%;

    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* Trading UI utilities */
@layer utilities {
  .panel-bg {
    background-color: hsl(var(--trading-panel));
  }
  
  .panel-border {
    border-color: hsl(var(--trading-border));
  }
  
  .text-trading-text {
    color: hsl(var(--trading-text));
  }
  
  .text-trading-text-secondary {
    color: hsl(var(--trading-text-secondary));
  }
  
  .bg-trading-bg {
    background-color: hsl(var(--trading-bg));
  }
  
  .bg-trading-panel {
    background-color: hsl(var(--trading-panel));
  }
  
  .bg-trading-border {
    background-color: hsl(var(--trading-border));
  }
  
  .border-trading-border {
    border-color: hsl(var(--trading-border));
  }
  
  .text-bull {
    color: hsl(var(--bull));
  }
  
  .text-bear {
    color: hsl(var(--bear));
  }
  
  .bg-bull {
    background-color: hsl(var(--bull));
  }
  
  .bg-bear {
    background-color: hsl(var(--bear));
  }
  
  .fill-chart-axis {
    fill: hsl(var(--chart-axis));
  }
}