{"permissions": {"allow": ["Bash(npm run lint)", "Bash(git log:*)", "Bash(npm run dev:*)", "Bash(npm install)", "Bash(npm run build:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(git push:*)", "Bash(rm:*)", "Bash(npm install:*)", "mcp__ide__getDiagnostics", "Bash(gh auth:*)", "<PERSON><PERSON>(powershell:*)", "Bash(dir /s /b *.log *.tmp *.cache dist build .next .turbo coverage)", "Bash(cd \"C:\\Users\\<USER>\\Documents\\elvis-fintech\\nexus-trader-terminal\")", "Bash(rm -rf src/data)", "WebFetch(domain:klinecharts.com)", "WebFetch(domain:merge.rocks)"], "deny": []}}