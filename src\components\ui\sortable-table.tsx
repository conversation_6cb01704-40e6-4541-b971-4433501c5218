import React, { useState, useMemo } from 'react';
import { ChevronUp, ChevronDown, ArrowUpDown } from 'lucide-react';

export interface SortConfig {
  key: string;
  direction: 'asc' | 'desc';
}

export interface Column<T> {
  key: keyof T;
  header: string;
  sortable?: boolean;
  render?: (value: any, item: T, index: number) => React.ReactNode;
  className?: string;
  ariaLabel?: (value: any, item: T) => string;
}

interface SortableTableProps<T> {
  data: T[];
  columns: Column<T>[];
  onRowClick?: (item: T, index: number) => void;
  onRowDoubleClick?: (item: T, index: number) => void;
  onRowContextMenu?: (event: React.MouseEvent, item: T, index: number) => void;
  emptyMessage?: string;
  emptyDescription?: string;
  tableId?: string;
  ariaLabel?: string;
  ariaDescription?: string;
  className?: string;
  rowClassName?: (item: T, index: number) => string;
  getRowId?: (item: T, index: number) => string;
  keyboardNavigation?: boolean;
}

export function SortableTable<T>({
  data,
  columns,
  onRowClick,
  onRowDoubleClick,
  onRowContextMenu,
  emptyMessage = "No records found.",
  emptyDescription = "Try adjusting your filters or add new data.",
  tableId,
  ariaLabel,
  ariaDescription,
  className = "",
  rowClassName,
  getRowId,
  keyboardNavigation = true
}: SortableTableProps<T>) {
  const [sortConfig, setSortConfig] = useState<SortConfig | null>(null);

  // Sort data based on current sort configuration
  const sortedData = useMemo(() => {
    if (!sortConfig) return data;

    return [...data].sort((a, b) => {
      const aValue = (a as any)[sortConfig.key];
      const bValue = (b as any)[sortConfig.key];

      if (aValue === null || aValue === undefined) return 1;
      if (bValue === null || bValue === undefined) return -1;

      let comparison = 0;
      
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        comparison = aValue.localeCompare(bValue);
      } else if (typeof aValue === 'number' && typeof bValue === 'number') {
        comparison = aValue - bValue;
      } else if (aValue instanceof Date && bValue instanceof Date) {
        comparison = aValue.getTime() - bValue.getTime();
      } else {
        comparison = String(aValue).localeCompare(String(bValue));
      }

      return sortConfig.direction === 'asc' ? comparison : -comparison;
    });
  }, [data, sortConfig]);

  const handleSort = (columnKey: string) => {
    const column = columns.find(col => col.key === columnKey);
    if (!column?.sortable) return;

    setSortConfig(prevConfig => {
      if (prevConfig?.key === columnKey) {
        if (prevConfig.direction === 'asc') {
          return { key: columnKey, direction: 'desc' };
        } else {
          return null; // Remove sort
        }
      } else {
        return { key: columnKey, direction: 'asc' };
      }
    });
  };

  const getSortIcon = (columnKey: string) => {
    if (!sortConfig || sortConfig.key !== columnKey) {
      return <ArrowUpDown className="w-4 h-4 text-gray-500" aria-hidden="true" />;
    }
    
    return sortConfig.direction === 'asc' 
      ? <ChevronUp className="w-4 h-4 text-blue-400" aria-hidden="true" />
      : <ChevronDown className="w-4 h-4 text-blue-400" aria-hidden="true" />;
  };

  const getSortAriaLabel = (column: Column<T>) => {
    if (!column.sortable) return undefined;
    
    if (!sortConfig || sortConfig.key !== column.key) {
      return `Sort by ${column.header}`;
    }
    
    if (sortConfig.direction === 'asc') {
      return `Sort by ${column.header} descending`;
    } else {
      return `Remove sort from ${column.header}`;
    }
  };

  const getSortAriaSort = (columnKey: string) => {
    if (!sortConfig || sortConfig.key !== columnKey) {
      return 'none';
    }
    return sortConfig.direction === 'asc' ? 'ascending' : 'descending';
  };

  return (
    <div className={`flex-1 overflow-y-auto border border-slate-600 rounded-lg ${className}`}>
      <table 
        id={tableId}
        className="w-full" 
        role="table" 
        aria-label={ariaLabel}
        aria-describedby={ariaDescription}
      >
        {ariaDescription && (
          <caption id={`${tableId}-description`} className="sr-only">
            {ariaDescription}
          </caption>
        )}
        
        <thead className="sticky top-0 bg-slate-800 z-10">
          <tr className="border-b border-slate-600">
            {columns.map((column) => (
              <th 
                key={String(column.key)} 
                scope="col" 
                className={`text-left p-2 sm:p-3 text-xs sm:text-sm font-medium text-gray-400 ${
                  column.sortable ? 'cursor-pointer hover:text-gray-200 transition-colors select-none' : ''
                } ${column.className || ''}`}
                onClick={() => column.sortable && handleSort(String(column.key))}
                aria-sort={column.sortable ? getSortAriaSort(String(column.key)) : undefined}
                aria-label={getSortAriaLabel(column)}
                tabIndex={column.sortable ? 0 : undefined}
                onKeyDown={(e) => {
                  if (column.sortable && (e.key === 'Enter' || e.key === ' ')) {
                    e.preventDefault();
                    handleSort(String(column.key));
                  }
                }}
              >
                <div className="flex items-center space-x-1">
                  <span>{column.header}</span>
                  {column.sortable && getSortIcon(String(column.key))}
                </div>
              </th>
            ))}
          </tr>
        </thead>
        
        <tbody>
          {sortedData.length === 0 ? (
            <tr>
              <td 
                colSpan={columns.length} 
                className="text-center py-8 text-gray-400" 
                role="cell"
              >
                <div className="flex flex-col items-center space-y-2">
                  <span>{emptyMessage}</span>
                  <span className="text-xs text-gray-500">{emptyDescription}</span>
                </div>
              </td>
            </tr>
          ) : (
            sortedData.map((item, index) => {
              const rowId = getRowId ? getRowId(item, index) : `row-${index}`;
              const defaultRowClassName = "border-b border-slate-700 hover:bg-slate-700/50 cursor-pointer select-none";
              const customRowClassName = rowClassName ? rowClassName(item, index) : "";
              const focusClassName = keyboardNavigation ? "focus:outline-none focus:bg-slate-700/70 focus:ring-2 focus:ring-blue-500 focus:ring-inset" : "";
              
              return (
                <tr 
                  key={rowId}
                  className={`${defaultRowClassName} ${customRowClassName} ${focusClassName}`}
                  onClick={() => onRowClick?.(item, index)}
                  onDoubleClick={() => onRowDoubleClick?.(item, index)}
                  onContextMenu={(e) => onRowContextMenu?.(e, item, index)}
                  tabIndex={keyboardNavigation ? 0 : undefined}
                  role="row"
                  aria-rowindex={index + 2}
                  onKeyDown={keyboardNavigation ? (e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      e.preventDefault();
                      onRowDoubleClick?.(item, index);
                    }
                  } : undefined}
                >
                  {columns.map((column) => {
                    const value = (item as any)[column.key];
                    const content = column.render ? column.render(value, item, index) : value;
                    const cellAriaLabel = column.ariaLabel ? column.ariaLabel(value, item) : undefined;
                    
                    return (
                      <td 
                        key={String(column.key)}
                        className={`p-2 sm:p-3 text-xs sm:text-sm text-white ${column.className || ''}`}
                        role="cell"
                        aria-label={cellAriaLabel}
                      >
                        {content}
                      </td>
                    );
                  })}
                </tr>
              );
            })
          )}
        </tbody>
      </table>
    </div>
  );
}

export default SortableTable;