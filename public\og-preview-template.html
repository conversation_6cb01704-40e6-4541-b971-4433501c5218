<!DOCTYPE html>
<html>
<head>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Arial', sans-serif;
        }
        .og-image {
            width: 1200px;
            height: 630px;
            background: linear-gradient(135deg, #0f172a 0%, #1e3a8a 50%, #0f172a 100%);
            position: relative;
            overflow: hidden;
        }
        .grid-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                linear-gradient(rgba(30, 64, 175, 0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(30, 64, 175, 0.1) 1px, transparent 1px);
            background-size: 40px 40px;
        }
        .content {
            position: relative;
            z-index: 2;
            padding: 80px;
            height: 100%;
            box-sizing: border-box;
            display: flex;
            align-items: center;
        }
        .left-section {
            flex: 1;
        }
        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #3b82f6, #06b6d4);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            font-weight: bold;
            color: white;
            margin-bottom: 40px;
            box-shadow: 0 0 30px rgba(59, 130, 246, 0.3);
        }
        .title {
            font-size: 56px;
            font-weight: bold;
            color: white;
            margin-bottom: 16px;
            line-height: 1.1;
        }
        .subtitle {
            font-size: 36px;
            font-weight: 600;
            color: #60a5fa;
            margin-bottom: 20px;
            line-height: 1.2;
        }
        .tagline {
            font-size: 22px;
            color: #94a3b8;
            margin-bottom: 40px;
            line-height: 1.4;
        }
        .features {
            display: flex;
            gap: 16px;
            margin-bottom: 40px;
        }
        .feature-pill {
            padding: 12px 24px;
            border-radius: 24px;
            font-size: 16px;
            font-weight: 500;
        }
        .pill-1 { background: rgba(30, 64, 175, 0.3); color: #60a5fa; }
        .pill-2 { background: rgba(5, 150, 105, 0.3); color: #34d399; }
        .pill-3 { background: rgba(124, 58, 237, 0.3); color: #a78bfa; }
        .cta {
            background: linear-gradient(135deg, #3b82f6, #06b6d4);
            color: white;
            padding: 16px 32px;
            border-radius: 32px;
            font-size: 20px;
            font-weight: 600;
            display: inline-block;
            box-shadow: 0 0 20px rgba(59, 130, 246, 0.4);
        }
        .right-section {
            width: 480px;
            height: 300px;
            position: relative;
        }
        .terminal {
            width: 100%;
            height: 100%;
            background: #1e293b;
            border-radius: 16px;
            border: 2px solid #475569;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }
        .terminal-header {
            background: #334155;
            height: 40px;
            display: flex;
            align-items: center;
            padding: 0 20px;
        }
        .traffic-lights {
            display: flex;
            gap: 8px;
        }
        .light {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }
        .red { background: #ef4444; }
        .yellow { background: #f59e0b; }
        .green { background: #10b981; }
        .terminal-title {
            color: #94a3b8;
            font-size: 14px;
            font-weight: 500;
            margin-left: auto;
            margin-right: auto;
        }
        .terminal-content {
            padding: 20px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            color: #e2e8f0;
        }
        .table-header {
            display: grid;
            grid-template-columns: 80px 80px 80px 80px 80px;
            gap: 10px;
            margin-bottom: 16px;
            color: #60a5fa;
            font-weight: bold;
        }
        .table-row {
            display: grid;
            grid-template-columns: 80px 80px 80px 80px 80px;
            gap: 10px;
            margin-bottom: 8px;
        }
        .positive { color: #10b981; }
        .negative { color: #ef4444; }
        .chart-area {
            margin-top: 20px;
            height: 60px;
            background: rgba(15, 20, 25, 0.5);
            border-radius: 8px;
            position: relative;
            overflow: hidden;
        }
        .chart-line {
            position: absolute;
            bottom: 10px;
            left: 10px;
            right: 10px;
            height: 2px;
            background: linear-gradient(90deg, #10b981, #34d399);
            border-radius: 1px;
        }
        .bottom-info {
            position: absolute;
            bottom: 30px;
            left: 80px;
            color: #64748b;
            font-size: 18px;
            font-weight: 500;
        }
        .website {
            position: absolute;
            bottom: 30px;
            right: 80px;
            color: #60a5fa;
            font-size: 16px;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="og-image">
        <div class="grid-bg"></div>
        <div class="content">
            <div class="left-section">
                <div class="logo">OTX</div>
                <div class="title">OTX Platform</div>
                <div class="subtitle">Complete Trading Infrastructure</div>
                <div class="tagline">Everything you need to start and scale your brokerage business</div>
                <div class="features">
                    <div class="feature-pill pill-1">CRM System</div>
                    <div class="feature-pill pill-2">Mobile App</div>
                    <div class="feature-pill pill-3">Dealer Terminal</div>
                </div>
                <div class="cta">Launch Your Brokerage</div>
            </div>
            <div class="right-section">
                <div class="terminal">
                    <div class="terminal-header">
                        <div class="traffic-lights">
                            <div class="light red"></div>
                            <div class="light yellow"></div>
                            <div class="light green"></div>
                        </div>
                        <div class="terminal-title">OTX Dealer Terminal</div>
                    </div>
                    <div class="terminal-content">
                        <div class="table-header">
                            <div>Symbol</div>
                            <div>Bid</div>
                            <div>Ask</div>
                            <div>Change</div>
                            <div>Volume</div>
                        </div>
                        <div class="table-row">
                            <div>EUR/USD</div>
                            <div>1.0856</div>
                            <div>1.0858</div>
                            <div class="positive">+0.0012</div>
                            <div>2.4M</div>
                        </div>
                        <div class="table-row">
                            <div>GBP/USD</div>
                            <div>1.2643</div>
                            <div>1.2645</div>
                            <div class="negative">-0.0008</div>
                            <div>1.8M</div>
                        </div>
                        <div class="table-row">
                            <div>USD/JPY</div>
                            <div>150.24</div>
                            <div>150.26</div>
                            <div class="positive">+0.15</div>
                            <div>3.1M</div>
                        </div>
                        <div class="table-row">
                            <div>XAU/USD</div>
                            <div>1987.45</div>
                            <div>1987.65</div>
                            <div class="positive">+12.30</div>
                            <div>856K</div>
                        </div>
                        <div class="chart-area">
                            <div class="chart-line"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="bottom-info">Professional Trading Infrastructure • CRM • Mobile Apps • Dealer Terminal</div>
        <div class="website">otx-dealer-terminal.vercel.app</div>
    </div>
</body>
</html>