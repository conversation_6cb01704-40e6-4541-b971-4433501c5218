import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { Position, Order, Deal, AccountSummary } from '../types/trading';

export type Timeframe = '1m' | '5m' | '15m' | '30m' | '1h' | '4h' | '1d';

export interface CurrencyPair {
  symbol: string;
  name: string;
  bid: number;
  ask: number;
  change: number;
  changePercent: number;
}

interface TradingContextType {
  selectedSymbol: string;
  selectedTimeframe: Timeframe;
  setSelectedSymbol: (symbol: string) => void;
  setSelectedTimeframe: (timeframe: Timeframe) => void;
  getCurrentPair: () => CurrencyPair | undefined;
  currencyPairs: CurrencyPair[];
  
  // New state for positions, orders, and deals
  openPositions: Position[];
  openOrders: Order[];
  positionHistory: Position[];
  orderHistory: Order[];
  dealHistory: Deal[];
  accountSummary: AccountSummary;
  
  // Counters
  openPositionsCount: number;
  openOrdersCount: number;
}

const TradingContext = createContext<TradingContextType | undefined>(undefined);

// Mock currency pairs data with real-time simulation
const initialCurrencyPairs: CurrencyPair[] = [
  { symbol: 'EURUSD', name: 'Euro / US Dollar', bid: 1.05421, ask: 1.05439, change: 0.00087, changePercent: 0.08 },
  { symbol: 'GBPUSD', name: 'British Pound / US Dollar', bid: 1.26892, ask: 1.26911, change: -0.00142, changePercent: -0.11 },
  { symbol: 'USDJPY', name: 'US Dollar / Japanese Yen', bid: 149.87, ask: 149.91, change: 0.23, changePercent: 0.15 },
  { symbol: 'USDCHF', name: 'US Dollar / Swiss Franc', bid: 0.88745, ask: 0.88763, change: 0.00098, changePercent: 0.11 },
  { symbol: 'EURJPY', name: 'Euro / Japanese Yen', bid: 158.42, ask: 158.58, change: -0.32, changePercent: -0.20 },
  { symbol: 'EURGBP', name: 'Euro / British Pound', bid: 0.83125, ask: 0.83142, change: 0.00076, changePercent: 0.09 },
  { symbol: 'EURCHF', name: 'Euro / Swiss Franc', bid: 0.93564, ask: 0.93581, change: 0.00145, changePercent: 0.16 },
  { symbol: 'AUDUSD', name: 'Australian Dollar / US Dollar', bid: 0.65321, ask: 0.65339, change: -0.00089, changePercent: -0.14 },
];

// Mock positions data
const initialOpenPositions: Position[] = [
  {
    positionId: 'POS_001',
    symbol: 'EURUSD',
    type: 'BUY',
    volume: 1.0,
    openPrice: 1.05234,
    currentPrice: 1.05421,
    sl: 1.04800,
    tp: 1.05800,
    pl: 187.00,
    openTime: '2025-08-02T08:15:23Z',
    swap: -2.50,
    commission: -10.00,
    comment: 'Long EUR trend'
  },
  {
    positionId: 'POS_002',
    symbol: 'GBPUSD',
    type: 'SELL',
    volume: 0.5,
    openPrice: 1.27045,
    currentPrice: 1.26892,
    sl: 1.27500,
    tp: 1.26200,
    pl: 76.50,
    openTime: '2025-08-02T09:45:12Z',
    swap: -1.20,
    commission: -5.00,
    comment: 'GBP weakness play'
  },
  {
    positionId: 'POS_003',
    symbol: 'USDJPY',
    type: 'BUY',
    volume: 2.0,
    openPrice: 149.12,
    currentPrice: 149.87,
    pl: 1002.00,
    openTime: '2025-08-02T07:30:45Z',
    swap: 0.80,
    commission: -20.00,
    comment: 'JPY intervention trade'
  }
];

// Mock orders data
const initialOpenOrders: Order[] = [
  {
    orderId: 'ORD_001',
    symbol: 'EURCHF',
    type: 'BUY LIMIT',
    volume: 1.0,
    orderPrice: 0.93200,
    sl: 0.92800,
    tp: 0.93800,
    currentPrice: '0.93564',
    state: 'PENDING',
    placementTime: '2025-08-02T10:15:30Z',
    comment: 'Support level entry'
  },
  {
    orderId: 'ORD_002',
    symbol: 'AUDUSD',
    type: 'SELL STOP',
    volume: 0.75,
    orderPrice: 0.65000,
    sl: 0.65500,
    tp: 0.64200,
    currentPrice: '0.65321',
    state: 'PENDING',
    placementTime: '2025-08-02T09:22:15Z',
    comment: 'Breakout trade'
  }
];

// Mock deal history
const initialDealHistory: Deal[] = [
  {
    executionTime: '2025-08-02T08:15:23Z',
    dealId: 'DEAL_001',
    symbol: 'EURUSD',
    type: 'buy',
    direction: 'in',
    volume: 1.0,
    executionPrice: 1.05234,
    orderId: 'ORD_999',
    commission: -10.00,
    swap: 0,
    profitLoss: 0,
    balance: 52150.75,
    comment: 'Position opened'
  },
  {
    executionTime: '2025-08-02T07:45:12Z',
    dealId: 'DEAL_002',
    symbol: 'GBPJPY',
    type: 'sell',
    direction: 'out',
    volume: 0.5,
    executionPrice: 190.45,
    commission: -5.00,
    swap: -1.20,
    profitLoss: 234.50,
    balance: 51926.25,
    comment: 'Position closed'
  }
];

// Mock account summary
const initialAccountSummary: AccountSummary = {
  balance: 50000.00,
  equity: 52150.75,
  fpl: 1265.50,
  usedMargin: 1250.50,
  usableMargin: 50900.25,
  account: 'MT5-001-LIVE',
  currency: 'USD',
  user: 'Trader001'
};

export const TradingProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [selectedSymbol, setSelectedSymbol] = useState<string>('EURUSD');
  const [selectedTimeframe, setSelectedTimeframe] = useState<Timeframe>('1h');
  const [currencyPairs, setCurrencyPairs] = useState<CurrencyPair[]>(initialCurrencyPairs);
  
  // New state for trading data
  const [openPositions, setOpenPositions] = useState<Position[]>(initialOpenPositions);
  const [openOrders, setOpenOrders] = useState<Order[]>(initialOpenOrders);
  const [positionHistory, setPositionHistory] = useState<Position[]>([]);
  const [orderHistory, setOrderHistory] = useState<Order[]>([]);
  const [dealHistory, setDealHistory] = useState<Deal[]>(initialDealHistory);
  const [accountSummary, setAccountSummary] = useState<AccountSummary>(initialAccountSummary);

  // Simulate real-time price updates with more realistic market movements
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrencyPairs(prevPairs => 
        prevPairs.map(pair => {
          // Different volatility for different pairs
          let volatility = 0.0008; // Base volatility
          if (pair.symbol.includes('JPY')) volatility = 0.002;
          if (pair.symbol.includes('GBP')) volatility = 0.0012;
          if (pair.symbol.includes('AUD')) volatility = 0.0010;
          
          // Market trend simulation (slight bias)
          const trendBias = Math.sin(Date.now() / 300000) * 0.3; // 5-minute cycle
          const randomFactor = (Math.random() - 0.5) * 2;
          const direction = randomFactor + trendBias;
          
          const changeAmount = volatility * pair.bid * direction * (0.5 + Math.random() * 0.5);
          
          const newBid = Math.max(0.01, pair.bid + changeAmount);
          
          // Fixed spread ratios to prevent ask price drift
          const spreadRatio = pair.symbol.includes('JPY') ? 0.0002 : 0.00015;
          const newSpread = newBid * spreadRatio;
          const newAsk = newBid + newSpread;
          
          const newChange = pair.change + changeAmount;
          const newChangePercent = (newChange / (pair.bid - pair.change)) * 100;

          return {
            ...pair,
            bid: Number(newBid.toFixed(pair.symbol.includes('JPY') ? 3 : 5)),
            ask: Number(newAsk.toFixed(pair.symbol.includes('JPY') ? 3 : 5)),
            change: Number(newChange.toFixed(pair.symbol.includes('JPY') ? 3 : 5)),
            changePercent: Number(newChangePercent.toFixed(2))
          };
        })
      );
    }, 1500); // Update every 1.5 seconds for more dynamic feel

    return () => clearInterval(interval);
  }, []);

  const getCurrentPair = (): CurrencyPair | undefined => {
    return currencyPairs.find(pair => pair.symbol === selectedSymbol);
  };

  const value: TradingContextType = {
    selectedSymbol,
    selectedTimeframe,
    setSelectedSymbol,
    setSelectedTimeframe,
    getCurrentPair,
    currencyPairs,
    
    // New trading data
    openPositions,
    openOrders,
    positionHistory,
    orderHistory,
    dealHistory,
    accountSummary,
    
    // Counters
    openPositionsCount: openPositions.length,
    openOrdersCount: openOrders.length,
  };

  return (
    <TradingContext.Provider value={value}>
      {children}
    </TradingContext.Provider>
  );
};

export const useTradingContext = (): TradingContextType => {
  const context = useContext(TradingContext);
  if (context === undefined) {
    throw new Error('useTradingContext must be used within a TradingProvider');
  }
  return context;
};