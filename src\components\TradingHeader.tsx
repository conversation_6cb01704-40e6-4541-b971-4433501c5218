import React, { useState, useRef, useEffect } from 'react';
import { ChevronDown, Search, Bell, Settings, User, Sun, Moon, Wifi, WifiOff, Activity, TrendingUp, TrendingDown, BarChart3 } from 'lucide-react';
import { useTradingContext } from '../contexts/TradingContext';

interface TradingHeaderProps {
  isDarkMode: boolean;
  toggleTheme: () => void;
}

export const TradingHeader: React.FC<TradingHeaderProps> = ({ isDarkMode, toggleTheme }) => {
  const { selectedSymbol, setSelectedSymbol, currencyPairs } = useTradingContext();
  const [showSettingsDropdown, setShowSettingsDropdown] = useState(false);
  const [showUserDropdown, setShowUserDropdown] = useState(false);
  const [showSymbolSearch, setShowSymbolSearch] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [isMarketOpen] = useState(true); // Mock data - could be calculated based on time
  const [isConnected] = useState(true); // Mock connection status
  const [connectionLatency] = useState(12); // Mock latency in ms
  const [dailyPnL] = useState(1247.85); // Mock daily P&L
  const [sessionType] = useState('DEMO'); // LIVE or DEMO

  const settingsRef = useRef<HTMLDivElement>(null);
  const userRef = useRef<HTMLDivElement>(null);
  const searchRef = useRef<HTMLDivElement>(null);

  const accountSummary = {
    balance: 50000.00,
    equity: 52150.75,
    margin: 1250.50,
    freeMargin: 50900.25,
    marginLevel: 4167.26,
    dailyPnL: dailyPnL,
    marginUsed: (1250.50 / 52150.75) * 100 // Percentage
  };

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (settingsRef.current && !settingsRef.current.contains(event.target as Node)) {
        setShowSettingsDropdown(false);
      }
      if (userRef.current && !userRef.current.contains(event.target as Node)) {
        setShowUserDropdown(false);
      }
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setShowSymbolSearch(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const filteredSymbols = currencyPairs.filter(pair => 
    pair.symbol.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <header className="h-16 bg-gradient-to-r from-trading-panel to-trading-bg-darker border-b border-trading-border flex items-center justify-between px-trading shadow-trading-lg">
      {/* Left Section - Branding + Status */}
      <div className="flex items-center space-x-6">
        {/* Enhanced Branding */}
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-gradient-to-br from-trading-accent to-trading-up rounded-lg flex items-center justify-center">
            <BarChart3 className="h-5 w-5 text-white" />
          </div>
          <div>
            <h1 className="text-lg font-bold text-trading-text tracking-tight">Nexus</h1>
            <div className="text-xs text-trading-text-dim -mt-0.5">Trader Terminal</div>
          </div>
          <div className={`px-2 py-0.5 rounded-full text-xs font-medium ${
            sessionType === 'DEMO' 
              ? 'bg-orange-500/20 text-orange-400 border border-orange-500/30' 
              : 'bg-trading-up/20 text-trading-up border border-trading-up/30'
          }`}>
            {sessionType}
          </div>
        </div>
        
        {/* Enhanced Status Indicators */}
        <div className="flex items-center space-x-6">
          {/* Market Status */}
          <div className="flex items-center space-x-2">
            <div className={`w-2 h-2 rounded-full ${isMarketOpen ? 'bg-trading-up animate-pulse' : 'bg-trading-down'}`} />
            <span className="text-xs text-trading-text-muted font-medium">
              {isMarketOpen ? 'Markets Open' : 'Markets Closed'}
            </span>
          </div>

          {/* Connection Status with Latency */}
          <div className="flex items-center space-x-2">
            {isConnected ? (
              <Wifi className="h-3 w-3 text-trading-up" />
            ) : (
              <WifiOff className="h-3 w-3 text-trading-down" />
            )}
            <span className="text-xs text-trading-text-muted font-medium">
              {isConnected ? `${connectionLatency}ms` : 'Reconnecting...'}
            </span>
          </div>

        </div>
      </div>

      {/* Center Section - Symbol Search */}
      <div className="flex items-center">
        {/* Enhanced Symbol Search */}
        <div ref={searchRef} className="relative">
          <div className="flex items-center bg-trading-bg-darker border border-trading-border rounded-lg px-3 py-2 focus-within:ring-1 focus-within:ring-trading-accent focus-within:border-trading-accent transition-all">
            <Search className="h-4 w-4 text-trading-text-muted mr-2" />
            <input
              type="text"
              placeholder="Search or select symbol..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onFocus={() => setShowSymbolSearch(true)}
              className="w-48 bg-transparent text-sm font-mono text-trading-text placeholder-trading-text-dim outline-none"
            />
            <div className="ml-2 px-2 py-1 bg-trading-accent/20 text-trading-accent text-xs rounded font-mono font-medium">
              {selectedSymbol}
            </div>
          </div>
          
          {showSymbolSearch && (
            <div className="absolute top-full left-0 mt-2 w-80 bg-trading-panel border border-trading-border rounded-lg shadow-trading-lg z-50 max-h-64 overflow-y-auto">
              <div className="p-2 border-b border-trading-border">
                <div className="text-xs text-trading-text-muted uppercase tracking-wide font-medium">Select Trading Pair</div>
              </div>
              {filteredSymbols.slice(0, 8).map((pair) => {
                const change = ((pair.ask - pair.bid) / pair.bid * 100);
                return (
                  <button
                    key={pair.symbol}
                    onClick={() => {
                      setSelectedSymbol(pair.symbol);
                      setSearchQuery('');
                      setShowSymbolSearch(false);
                    }}
                    className="w-full px-3 py-3 text-left hover:bg-trading-border/20 transition-colors flex items-center justify-between group"
                  >
                    <div className="flex items-center space-x-3">
                      <div className="font-mono font-bold text-sm text-trading-text group-hover:text-trading-accent transition-colors">
                        {pair.symbol}
                      </div>
                      <div className="text-xs text-trading-text-dim font-mono">
                        Spread: {(pair.ask - pair.bid).toFixed(pair.symbol.includes('JPY') ? 3 : 5)}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-mono text-sm text-trading-text">
                        {pair.ask.toFixed(pair.symbol.includes('JPY') ? 3 : 5)}
                      </div>
                      <div className={`text-xs font-mono font-medium ${
                        change >= 0 ? 'text-trading-up' : 'text-trading-down'
                      }`}>
                        {change >= 0 ? '+' : ''}{change.toFixed(2)}%
                      </div>
                    </div>
                  </button>
                );
              })}
            </div>
          )}
        </div>
      </div>

      {/* Right Section - Enhanced Account + Controls */}
      <div className="flex items-center space-x-6 text-xs">
        {/* Daily P&L Indicator */}
        <div className="flex items-center space-x-2 px-3 py-2 bg-trading-bg-darker/50 rounded-lg border border-trading-border">
          {dailyPnL >= 0 ? (
            <TrendingUp className="h-4 w-4 text-trading-up" />
          ) : (
            <TrendingDown className="h-4 w-4 text-trading-down" />
          )}
          <div>
            <div className="text-trading-text-dim uppercase text-xs">Daily P&L</div>
            <div className={`font-bold font-mono text-sm ${
              dailyPnL >= 0 ? 'text-trading-up' : 'text-trading-down'
            }`}>
              {dailyPnL >= 0 ? '+' : ''}${dailyPnL.toFixed(2)}
            </div>
          </div>
        </div>

        {/* Enhanced Account Summary */}
        <div className="flex items-center space-x-4">
          <div className="text-center">
            <div className="text-trading-text-dim uppercase tracking-wide">Balance</div>
            <div className="font-medium font-mono text-trading-text">${accountSummary.balance.toLocaleString()}</div>
          </div>
          <div className="text-center">
            <div className="text-trading-text-dim uppercase tracking-wide">Equity</div>
            <div className="font-medium font-mono text-trading-up">${accountSummary.equity.toLocaleString()}</div>
          </div>
          <div className="text-center">
            <div className="text-trading-text-dim uppercase tracking-wide">Margin</div>
            <div className="font-medium font-mono text-trading-text">${accountSummary.margin.toLocaleString()}</div>
            <div className="w-16 h-1 bg-trading-border rounded-full mt-1">
              <div 
                className="h-full bg-trading-accent rounded-full transition-all duration-300"
                style={{ width: `${Math.min(accountSummary.marginUsed, 100)}%` }}
              />
            </div>
          </div>
          <div className="text-center">
            <div className="text-trading-text-dim uppercase tracking-wide">Free Margin</div>
            <div className="font-medium font-mono text-trading-text">${accountSummary.freeMargin.toLocaleString()}</div>
          </div>
          <div className="text-center">
            <div className="text-trading-text-dim uppercase tracking-wide">Margin Level</div>
            <div className="font-medium font-mono text-trading-up">{accountSummary.marginLevel.toFixed(1)}%</div>
          </div>
        </div>
        
        {/* Control Icons */}
        <div className="flex items-center space-x-3">
          {/* Enhanced Notifications */}
          <button className="p-2 text-trading-text-muted hover:text-trading-text transition-colors rounded-lg hover:bg-trading-border/20 relative">
            <Bell className="h-4 w-4" />
            <span className="absolute -top-1 -right-1 bg-trading-down text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-bold animate-pulse">3</span>
          </button>

          {/* Quick Stats */}
          <div className="flex items-center space-x-2 text-xs">
            <Activity className="h-3 w-3 text-trading-accent" />
            <span className="text-trading-text-muted font-medium">24h Vol: $2.4M</span>
          </div>

          {/* Settings Dropdown */}
          <div ref={settingsRef} className="relative">
            <button
              onClick={() => setShowSettingsDropdown(!showSettingsDropdown)}
              className="p-2 text-trading-text-muted hover:text-trading-text transition-colors rounded-lg hover:bg-trading-border/20"
            >
              <Settings className="h-4 w-4" />
            </button>
            
            {showSettingsDropdown && (
              <div className="absolute right-0 top-full mt-1 w-48 bg-trading-panel border border-trading-border rounded-trading shadow-trading-lg z-50">
                <div className="py-1">
                  <button
                    onClick={() => {
                      toggleTheme();
                      setShowSettingsDropdown(false);
                    }}
                    className="w-full px-3 py-2 text-left text-sm hover:bg-trading-border/20 transition-colors flex items-center space-x-2"
                  >
                    {isDarkMode ? <Sun className="h-4 w-4" /> : <Moon className="h-4 w-4" />}
                    <span>{isDarkMode ? 'Light Mode' : 'Dark Mode'}</span>
                  </button>
                  <button className="w-full px-3 py-2 text-left text-sm hover:bg-trading-border/20 transition-colors">
                    Preferences
                  </button>
                  <button className="w-full px-3 py-2 text-left text-sm hover:bg-trading-border/20 transition-colors">
                    Layout Settings
                  </button>
                </div>
              </div>
            )}
          </div>

          {/* User Dropdown */}
          <div ref={userRef} className="relative">
            <button
              onClick={() => setShowUserDropdown(!showUserDropdown)}
              className="p-2 text-trading-text-muted hover:text-trading-text transition-colors rounded-lg hover:bg-trading-border/20 flex items-center space-x-2"
            >
              <div className="w-6 h-6 bg-gradient-to-br from-trading-accent to-trading-up rounded-full flex items-center justify-center">
                <User className="h-3 w-3 text-white" />
              </div>
              <ChevronDown className="h-3 w-3" />
            </button>
            
            {showUserDropdown && (
              <div className="absolute right-0 top-full mt-1 w-48 bg-trading-panel border border-trading-border rounded-trading shadow-trading-lg z-50">
                <div className="py-1">
                  <div className="px-3 py-2 text-sm text-trading-text-muted border-b border-trading-border">
                    Account: Demo123
                  </div>
                  <button className="w-full px-3 py-2 text-left text-sm hover:bg-trading-border/20 transition-colors">
                    Account Settings
                  </button>
                  <button className="w-full px-3 py-2 text-left text-sm hover:bg-trading-border/20 transition-colors">
                    Trading History
                  </button>
                  <button className="w-full px-3 py-2 text-left text-sm hover:bg-trading-border/20 transition-colors text-trading-down">
                    Logout
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  );
};