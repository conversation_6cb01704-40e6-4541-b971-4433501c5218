import React from 'react';
import { useTradingContext } from '../contexts/TradingContext';
import { Card } from './ui/card';
import { FileText, CheckCircle, XCircle, Clock } from 'lucide-react';

// Mock historical orders data for demonstration
const mockOrderHistory = [
  {
    orderId: 'ORD_H001',
    symbol: 'EURUSD',
    type: 'BUY LIMIT',
    volume: 1.0,
    orderPrice: 1.04800,
    sl: 1.04400,
    tp: 1.05200,
    state: 'FILLED',
    placementTime: '2025-08-01T08:30:00Z',
    executionTime: '2025-08-01T09:15:23Z',
    fillPrice: 1.04795,
    comment: 'Support bounce entry'
  },
  {
    orderId: 'ORD_H002',
    symbol: 'GBPUSD',
    type: 'SELL STOP',
    volume: 0.5,
    orderPrice: 1.26500,
    sl: 1.27000,
    tp: 1.25800,
    state: 'CANCELLED',
    placementTime: '2025-08-01T12:45:00Z',
    executionTime: '2025-08-01T14:30:15Z',
    comment: 'Market conditions changed'
  },
  {
    orderId: 'ORD_H003',
    symbol: 'USDJPY',
    type: 'BUY MARKET',
    volume: 2.0,
    orderPrice: 149.12,
    state: 'FILLED',
    placementTime: '2025-08-01T07:30:00Z',
    executionTime: '2025-08-01T07:30:05Z',
    fillPrice: 149.15,
    comment: 'Quick scalp trade'
  },
  {
    orderId: 'ORD_H004',
    symbol: 'EURCHF',
    type: 'BUY LIMIT',
    volume: 0.75,
    orderPrice: 0.93200,
    sl: 0.92800,
    tp: 0.93800,
    state: 'EXPIRED',
    placementTime: '2025-07-31T16:00:00Z',
    executionTime: '2025-08-01T16:00:00Z',
    comment: 'Daily limit order'
  },
  {
    orderId: 'ORD_H005',
    symbol: 'AUDUSD',
    type: 'SELL MARKET',
    volume: 1.5,
    orderPrice: 0.65450,
    state: 'PARTIALLY_FILLED',
    placementTime: '2025-07-31T14:20:00Z',
    executionTime: '2025-07-31T14:20:12Z',
    fillPrice: 0.65448,
    comment: 'Large order partial fill'
  }
];

export const OrderHistory: React.FC = () => {
  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const formatPrice = (price: number, symbol: string) => {
    const decimals = symbol.includes('JPY') ? 3 : 5;
    return price.toFixed(decimals);
  };

  const getStatusIcon = (state: string) => {
    switch (state) {
      case 'FILLED':
        return <CheckCircle className="h-3 w-3 text-green-500" />;
      case 'CANCELLED':
        return <XCircle className="h-3 w-3 text-red-500" />;
      case 'EXPIRED':
        return <Clock className="h-3 w-3 text-gray-500" />;
      case 'PARTIALLY_FILLED':
        return <CheckCircle className="h-3 w-3 text-yellow-500" />;
      default:
        return <Clock className="h-3 w-3 text-blue-500" />;
    }
  };

  const getStatusColor = (state: string) => {
    switch (state) {
      case 'FILLED':
        return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-300';
      case 'CANCELLED':
        return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-300';
      case 'EXPIRED':
        return 'text-gray-600 bg-gray-100 dark:bg-gray-800 dark:text-gray-400';
      case 'PARTIALLY_FILLED':
        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300';
      default:
        return 'text-blue-600 bg-blue-100 dark:bg-blue-900 dark:text-blue-300';
    }
  };

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center space-x-2">
          <FileText className="h-4 w-4 text-gray-500" />
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">
            Order History
          </h3>
          <span className="text-xs text-gray-500 dark:text-gray-400">
            ({mockOrderHistory.length} orders)
          </span>
        </div>
        <div className="flex items-center space-x-2 text-xs">
          <span className="text-gray-500 dark:text-gray-400">Filter:</span>
          <select className="bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded px-2 py-1 text-xs">
            <option>All States</option>
            <option>FILLED</option>
            <option>CANCELLED</option>
            <option>EXPIRED</option>
          </select>
        </div>
      </div>

      {/* Table */}
      <div className="flex-1 overflow-auto">
        <table className="w-full text-xs">
          <thead className="bg-gray-50 dark:bg-gray-800 sticky top-0">
            <tr>
              <th className="px-3 py-2 text-left font-medium text-gray-700 dark:text-gray-300">Order ID</th>
              <th className="px-3 py-2 text-left font-medium text-gray-700 dark:text-gray-300">Symbol</th>
              <th className="px-3 py-2 text-left font-medium text-gray-700 dark:text-gray-300">Type</th>
              <th className="px-3 py-2 text-right font-medium text-gray-700 dark:text-gray-300">Volume</th>
              <th className="px-3 py-2 text-right font-medium text-gray-700 dark:text-gray-300">Order Price</th>
              <th className="px-3 py-2 text-right font-medium text-gray-700 dark:text-gray-300">Fill Price</th>
              <th className="px-3 py-2 text-left font-medium text-gray-700 dark:text-gray-300">Placed</th>
              <th className="px-3 py-2 text-left font-medium text-gray-700 dark:text-gray-300">Executed</th>
              <th className="px-3 py-2 text-left font-medium text-gray-700 dark:text-gray-300">Status</th>
              <th className="px-3 py-2 text-left font-medium text-gray-700 dark:text-gray-300">Comment</th>
            </tr>
          </thead>
          <tbody>
            {mockOrderHistory.map((order, index) => (
              <tr
                key={order.orderId}
                className={`border-b border-gray-100 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800/50 ${
                  index % 2 === 0 ? 'bg-white dark:bg-gray-900' : 'bg-gray-50/50 dark:bg-gray-800/30'
                }`}
              >
                <td className="px-3 py-2 font-mono text-gray-600 dark:text-gray-400">
                  {order.orderId}
                </td>
                <td className="px-3 py-2 font-semibold text-gray-900 dark:text-white">
                  {order.symbol}
                </td>
                <td className="px-3 py-2">
                  <span className={`px-1.5 py-0.5 rounded text-xs font-medium ${
                    order.type.includes('BUY')
                      ? 'bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300'
                      : 'bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-300'
                  }`}>
                    {order.type}
                  </span>
                </td>
                <td className="px-3 py-2 text-right text-gray-900 dark:text-white">
                  {order.volume}
                </td>
                <td className="px-3 py-2 text-right font-mono text-gray-900 dark:text-white">
                  {formatPrice(order.orderPrice, order.symbol)}
                </td>
                <td className="px-3 py-2 text-right font-mono text-gray-900 dark:text-white">
                  {order.fillPrice ? formatPrice(order.fillPrice, order.symbol) : '-'}
                </td>
                <td className="px-3 py-2 text-gray-600 dark:text-gray-400">
                  {formatDateTime(order.placementTime)}
                </td>
                <td className="px-3 py-2 text-gray-600 dark:text-gray-400">
                  {order.executionTime ? formatDateTime(order.executionTime) : '-'}
                </td>
                <td className="px-3 py-2">
                  <div className="flex items-center space-x-1">
                    {getStatusIcon(order.state)}
                    <span className={`px-1.5 py-0.5 rounded text-xs font-medium ${getStatusColor(order.state)}`}>
                      {order.state.replace('_', ' ')}
                    </span>
                  </div>
                </td>
                <td className="px-3 py-2 text-gray-600 dark:text-gray-400 max-w-32 truncate">
                  {order.comment}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Summary Footer */}
      <div className="border-t border-gray-200 dark:border-gray-700 p-3 bg-gray-50 dark:bg-gray-800">
        <div className="flex justify-between items-center text-xs">
          <span className="text-gray-600 dark:text-gray-400">
            Total: {mockOrderHistory.length} orders
          </span>
          <div className="flex items-center space-x-4">
            <span className="text-green-600">
              Filled: {mockOrderHistory.filter(o => o.state === 'FILLED').length}
            </span>
            <span className="text-red-600">
              Cancelled: {mockOrderHistory.filter(o => o.state === 'CANCELLED').length}
            </span>
            <span className="text-gray-600 dark:text-gray-400">
              Expired: {mockOrderHistory.filter(o => o.state === 'EXPIRED').length}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};