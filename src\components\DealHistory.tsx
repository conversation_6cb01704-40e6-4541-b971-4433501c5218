import React from 'react';
import { useTradingContext } from '../contexts/TradingContext';
import { Card } from './ui/card';
import { Receipt, ArrowUpCircle, ArrowDownCircle, DollarSign } from 'lucide-react';

// Mock deal history data with more comprehensive trading activity
const mockDealHistory = [
  {
    executionTime: '2025-08-02T08:15:23Z',
    dealId: 'DEAL_001',
    symbol: 'EURUSD',
    type: 'buy' as const,
    direction: 'in' as const,
    volume: 1.0,
    executionPrice: 1.05234,
    orderId: 'ORD_999',
    commission: -10.00,
    swap: 0,
    profitLoss: 0,
    balance: 52150.75,
    comment: 'Position opened'
  },
  {
    executionTime: '2025-08-02T07:45:12Z',
    dealId: 'DEAL_002',
    symbol: 'GBPJPY',
    type: 'sell' as const,
    direction: 'out' as const,
    volume: 0.5,
    executionPrice: 190.45,
    commission: -5.00,
    swap: -1.20,
    profitLoss: 234.50,
    balance: 51926.25,
    comment: 'Position closed'
  },
  {
    executionTime: '2025-08-01T16:30:45Z',
    dealId: 'DEAL_003',
    type: 'balance' as const,
    commission: 0,
    swap: 0,
    profitLoss: 1000.00,
    balance: 51691.75,
    comment: 'Deposit - Wire Transfer'
  },
  {
    executionTime: '2025-08-01T14:22:18Z',
    dealId: 'DEAL_004',
    symbol: 'USDCHF',
    type: 'sell' as const,
    direction: 'out' as const,
    volume: 0.75,
    executionPrice: 0.89123,
    commission: -7.50,
    swap: 0.40,
    profitLoss: -119.25,
    balance: 50691.75,
    comment: 'Stop loss triggered'
  },
  {
    executionTime: '2025-08-01T12:15:30Z',
    dealId: 'DEAL_005',
    symbol: 'AUDUSD',
    type: 'buy' as const,
    direction: 'out' as const,
    volume: 2.0,
    executionPrice: 0.65432,
    commission: -20.00,
    swap: 1.60,
    profitLoss: 686.00,
    balance: 50818.50,
    comment: 'Take profit hit'
  },
  {
    executionTime: '2025-08-01T09:45:22Z',
    dealId: 'DEAL_006',
    symbol: 'EURJPY',
    type: 'buy' as const,
    direction: 'in' as const,
    volume: 1.5,
    executionPrice: 158.42,
    commission: -15.00,
    swap: 0,
    profitLoss: 0,
    balance: 50147.50,
    comment: 'Market order execution'
  },
  {
    executionTime: '2025-07-31T18:20:15Z',
    dealId: 'DEAL_007',
    type: 'balance' as const,
    commission: -25.00,
    swap: 0,
    profitLoss: -25.00,
    balance: 50162.50,
    comment: 'Monthly account fee'
  }
];

export const DealHistory: React.FC = () => {
  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const formatPrice = (price: number, symbol?: string) => {
    if (!symbol) return price.toFixed(2);
    const decimals = symbol.includes('JPY') ? 3 : 5;
    return price.toFixed(decimals);
  };

  const getDealIcon = (deal: any) => {
    if (deal.type === 'balance') {
      return deal.profitLoss > 0 ? 
        <ArrowUpCircle className="h-3 w-3 text-green-500" /> : 
        <ArrowDownCircle className="h-3 w-3 text-red-500" />;
    }
    
    if (deal.direction === 'in') {
      return <DollarSign className="h-3 w-3 text-blue-500" />;
    }
    
    return deal.profitLoss > 0 ? 
      <ArrowUpCircle className="h-3 w-3 text-green-500" /> : 
      <ArrowDownCircle className="h-3 w-3 text-red-500" />;
  };

  const getDealTypeDisplay = (deal: any) => {
    if (deal.type === 'balance') return 'BALANCE';
    return `${deal.type.toUpperCase()} ${deal.direction?.toUpperCase() || ''}`.trim();
  };

  const getDealTypeColor = (deal: any) => {
    if (deal.type === 'balance') {
      return deal.profitLoss > 0 ? 
        'bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300' :
        'bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-300';
    }
    
    if (deal.direction === 'in') {
      return 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300';
    }
    
    return deal.profitLoss >= 0 ? 
      'bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300' :
      'bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-300';
  };

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center space-x-2">
          <Receipt className="h-4 w-4 text-gray-500" />
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">
            Deal History
          </h3>
          <span className="text-xs text-gray-500 dark:text-gray-400">
            ({mockDealHistory.length} transactions)
          </span>
        </div>
        <div className="flex items-center space-x-2 text-xs">
          <span className="text-gray-500 dark:text-gray-400">Filter:</span>
          <select className="bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded px-2 py-1 text-xs">
            <option>All Types</option>
            <option>Trades Only</option>
            <option>Balance Only</option>
            <option>Profitable</option>
          </select>
        </div>
      </div>

      {/* Table */}
      <div className="flex-1 overflow-auto">
        <table className="w-full text-xs">
          <thead className="bg-gray-50 dark:bg-gray-800 sticky top-0">
            <tr>
              <th className="px-3 py-2 text-left font-medium text-gray-700 dark:text-gray-300">Deal ID</th>
              <th className="px-3 py-2 text-left font-medium text-gray-700 dark:text-gray-300">Time</th>
              <th className="px-3 py-2 text-left font-medium text-gray-700 dark:text-gray-300">Symbol</th>
              <th className="px-3 py-2 text-left font-medium text-gray-700 dark:text-gray-300">Type</th>
              <th className="px-3 py-2 text-right font-medium text-gray-700 dark:text-gray-300">Volume</th>
              <th className="px-3 py-2 text-right font-medium text-gray-700 dark:text-gray-300">Price</th>
              <th className="px-3 py-2 text-right font-medium text-gray-700 dark:text-gray-300">Commission</th>
              <th className="px-3 py-2 text-right font-medium text-gray-700 dark:text-gray-300">Swap</th>
              <th className="px-3 py-2 text-right font-medium text-gray-700 dark:text-gray-300">Profit/Loss</th>
              <th className="px-3 py-2 text-right font-medium text-gray-700 dark:text-gray-300">Balance</th>
              <th className="px-3 py-2 text-left font-medium text-gray-700 dark:text-gray-300">Comment</th>
            </tr>
          </thead>
          <tbody>
            {mockDealHistory.map((deal, index) => (
              <tr
                key={deal.dealId}
                className={`border-b border-gray-100 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800/50 ${
                  index % 2 === 0 ? 'bg-white dark:bg-gray-900' : 'bg-gray-50/50 dark:bg-gray-800/30'
                }`}
              >
                <td className="px-3 py-2 font-mono text-gray-600 dark:text-gray-400">
                  {deal.dealId}
                </td>
                <td className="px-3 py-2 text-gray-600 dark:text-gray-400">
                  {formatDateTime(deal.executionTime)}
                </td>
                <td className="px-3 py-2 font-semibold text-gray-900 dark:text-white">
                  {deal.symbol || '-'}
                </td>
                <td className="px-3 py-2">
                  <div className="flex items-center space-x-1">
                    {getDealIcon(deal)}
                    <span className={`px-1.5 py-0.5 rounded text-xs font-medium ${getDealTypeColor(deal)}`}>
                      {getDealTypeDisplay(deal)}
                    </span>
                  </div>
                </td>
                <td className="px-3 py-2 text-right text-gray-900 dark:text-white">
                  {deal.volume || '-'}
                </td>
                <td className="px-3 py-2 text-right font-mono text-gray-900 dark:text-white">
                  {deal.executionPrice ? formatPrice(deal.executionPrice, deal.symbol) : '-'}
                </td>
                <td className="px-3 py-2 text-right text-gray-600 dark:text-gray-400">
                  {deal.commission !== 0 ? `$${deal.commission.toFixed(2)}` : '-'}
                </td>
                <td className="px-3 py-2 text-right text-gray-600 dark:text-gray-400">
                  {deal.swap !== 0 ? `$${deal.swap.toFixed(2)}` : '-'}
                </td>
                <td className="px-3 py-2 text-right">
                  <span className={`font-semibold ${
                    deal.profitLoss > 0 ? 'text-green-600' : 
                    deal.profitLoss < 0 ? 'text-red-600' : 'text-gray-600 dark:text-gray-400'
                  }`}>
                    {deal.profitLoss !== 0 ? 
                      `${deal.profitLoss > 0 ? '+' : ''}$${deal.profitLoss.toFixed(2)}` : 
                      '-'
                    }
                  </span>
                </td>
                <td className="px-3 py-2 text-right font-semibold text-gray-900 dark:text-white">
                  ${deal.balance.toLocaleString()}
                </td>
                <td className="px-3 py-2 text-gray-600 dark:text-gray-400 max-w-32 truncate">
                  {deal.comment}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Summary Footer */}
      <div className="border-t border-gray-200 dark:border-gray-700 p-3 bg-gray-50 dark:bg-gray-800">
        <div className="flex justify-between items-center text-xs">
          <span className="text-gray-600 dark:text-gray-400">
            Total: {mockDealHistory.length} transactions
          </span>
          <div className="flex items-center space-x-4">
            <span className="text-blue-600">
              Trades: {mockDealHistory.filter(d => d.type !== 'balance').length}
            </span>
            <span className="text-purple-600">
              Balance Ops: {mockDealHistory.filter(d => d.type === 'balance').length}
            </span>
            <span className="font-semibold text-gray-900 dark:text-white">
              Net P&L: ${mockDealHistory.reduce((sum, d) => sum + d.profitLoss, 0).toFixed(2)}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};