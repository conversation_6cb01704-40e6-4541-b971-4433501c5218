import React from 'react';

const mockMessages = [
  { id: 1, type: 'info', message: 'Market opening soon', timestamp: '14:30' },
  { id: 2, type: 'success', message: 'Order executed successfully', timestamp: '14:25' },
  { id: 3, type: 'warning', message: 'High volatility detected', timestamp: '14:20' },
  { id: 4, type: 'info', message: 'Price alert triggered', timestamp: '14:15' },
];

export const MessagesPanel: React.FC = () => {
  return (
    <div className="h-64 bg-white dark:bg-gray-900 border-l border-b border-gray-200 dark:border-gray-700 p-3 overflow-hidden">
      <div className="h-full flex flex-col">
        {/* Messages Header */}
        <div className="mb-2">
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">Messages</h3>
        </div>
        
        {/* Messages List */}
        <div className="flex-1 overflow-y-auto space-y-1">
          {mockMessages.map((message) => (
            <div key={message.id} className="flex items-start space-x-2 text-xs">
              <div className={`w-2 h-2 rounded-full mt-1 ${
                message.type === 'success' ? 'bg-green-500' :
                message.type === 'warning' ? 'bg-yellow-500' :
                message.type === 'error' ? 'bg-red-500' : 'bg-blue-500'
              }`} />
              <div className="flex-1 min-w-0">
                <div className="text-gray-900 dark:text-white text-xs truncate">
                  {message.message}
                </div>
                <div className="text-gray-500 dark:text-gray-400 text-xs">
                  {message.timestamp}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};