import React, { useState, useEffect } from 'react';
import { Search, Star } from 'lucide-react';
import { useTradingContext } from '../contexts/TradingContext';

const PairRow: React.FC<{ pair: any, isSelected: boolean, onClick: () => void }> = ({ pair, isSelected, onClick }) => {
  const [priceFlash, setPriceFlash] = useState<'up' | 'down' | null>(null);
  const [prevMidPrice, setPrevMidPrice] = useState((pair.bid + pair.ask) / 2);

  useEffect(() => {
    const currentMidPrice = (pair.bid + pair.ask) / 2;
    if (currentMidPrice !== prevMidPrice) {
      setPriceFlash(currentMidPrice > prevMidPrice ? 'up' : 'down');
      setPrevMidPrice(currentMidPrice);
      
      const timer = setTimeout(() => setPriceFlash(null), 300);
      return () => clearTimeout(timer);
    }
  }, [pair.bid, pair.ask, prevMidPrice]);

  return (
    <tr 
      className={`cursor-pointer transition-all duration-200 hover:bg-trading-border/20 ${
        isSelected ? 'bg-trading-accent-bg border-l-2 border-trading-accent' : ''
      } ${
        priceFlash === 'up' ? 'bg-trading-up-bg' : 
        priceFlash === 'down' ? 'bg-trading-down-bg' : ''
      }`}
      onClick={onClick}
    >
      <td className="px-2 py-1 text-xs font-medium text-trading-text font-mono">
        {pair.symbol}
      </td>
      <td className={`px-2 py-1 text-xs font-mono transition-colors duration-200 ${
        priceFlash === 'down' ? 'text-trading-down font-semibold' : 
        priceFlash === 'up' ? 'text-trading-up font-semibold' : 'text-trading-text-muted'
      }`}>
        {pair.bid.toFixed(pair.symbol.includes('JPY') ? 3 : 5)}
      </td>
      <td className={`px-2 py-1 text-xs font-mono transition-colors duration-200 ${
        priceFlash === 'up' ? 'text-trading-up font-semibold' : 
        priceFlash === 'down' ? 'text-trading-down font-semibold' : 'text-trading-text-muted'
      }`}>
        {pair.ask.toFixed(pair.symbol.includes('JPY') ? 3 : 5)}
      </td>
      <td className={`px-2 py-1 text-xs font-mono font-medium flex items-center ${
        pair.changePercent >= 0 ? 'text-trading-up' : 'text-trading-down'
      }`}>
        <span className="mr-1">
          {pair.changePercent >= 0 ? '↗' : '↘'}
        </span>
        {Math.abs(pair.changePercent).toFixed(2)}%
      </td>
    </tr>
  );
};

export const Watchlist: React.FC = () => {
  const { selectedSymbol, setSelectedSymbol, currencyPairs } = useTradingContext();
  
  const eurPairs = currencyPairs.filter(pair => pair.symbol.startsWith('EUR'));
  const usdPairs = currencyPairs.filter(pair => pair.symbol.includes('USD') && !pair.symbol.startsWith('EUR'));

  return (
    <div className="flex-1 bg-trading-panel border-r border-trading-border flex flex-col overflow-hidden shadow-trading">
      <div className="p-trading border-b border-trading-border bg-trading-bg-darker">
        <h3 className="text-sm font-medium text-trading-text">Watchlist</h3>
      </div>
      
      <div className="flex-1 overflow-y-auto">
        {/* EUR Pairs Section */}
        <div className="p-trading-sm">
          <div className="text-xs font-medium text-trading-text-dim mb-2 uppercase tracking-wide">EUR</div>
          <table className="w-full text-xs table-fixed">
            <thead>
              <tr className="text-trading-text-dim border-b border-trading-border">
                <th className="px-2 py-1 text-left font-medium w-14">Symbol</th>
                <th className="px-2 py-1 text-left font-medium w-12">Bid</th>
                <th className="px-2 py-1 text-left font-medium w-12">Ask</th>
                <th className="px-2 py-1 text-left font-medium w-10">%</th>
              </tr>
            </thead>
            <tbody>
              {eurPairs.map((pair) => (
                <PairRow
                  key={pair.symbol}
                  pair={pair}
                  isSelected={selectedSymbol === pair.symbol}
                  onClick={() => setSelectedSymbol(pair.symbol)}
                />
              ))}
            </tbody>
          </table>
        </div>

        {/* USD Pairs Section */}
        <div className="p-trading-sm">
          <div className="text-xs font-medium text-trading-text-dim mb-2 uppercase tracking-wide">USD</div>
          <table className="w-full text-xs table-fixed">
            <thead>
              <tr className="text-trading-text-dim border-b border-trading-border">
                <th className="px-2 py-1 text-left font-medium w-14">Symbol</th>
                <th className="px-2 py-1 text-left font-medium w-12">Bid</th>
                <th className="px-2 py-1 text-left font-medium w-12">Ask</th>
                <th className="px-2 py-1 text-left font-medium w-10">%</th>
              </tr>
            </thead>
            <tbody>
              {usdPairs.map((pair) => (
                <PairRow
                  key={pair.symbol}
                  pair={pair}
                  isSelected={selectedSymbol === pair.symbol}
                  onClick={() => setSelectedSymbol(pair.symbol)}
                />
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};