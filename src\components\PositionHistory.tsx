import React from 'react';
import { useTradingContext } from '../contexts/TradingContext';
import { Card } from './ui/card';
import { Calendar, TrendingUp, TrendingDown } from 'lucide-react';

// Mock historical positions data for demonstration
const mockPositionHistory = [
  {
    positionId: 'POS_H001',
    symbol: 'GBPJPY',
    type: 'SELL' as const,
    volume: 0.5,
    openPrice: 190.45,
    closePrice: 189.89,
    openTime: '2025-08-01T14:30:00Z',
    closeTime: '2025-08-01T16:45:00Z',
    pl: 234.50,
    swap: -1.20,
    commission: -5.00,
    comment: 'Technical analysis trade'
  },
  {
    positionId: 'POS_H002',
    symbol: 'EURUSD',
    type: 'BUY' as const,
    volume: 1.0,
    openPrice: 1.04895,
    closePrice: 1.05234,
    openTime: '2025-08-01T09:15:00Z',
    closeTime: '2025-08-01T12:30:00Z',
    pl: 339.00,
    swap: -0.80,
    commission: -10.00,
    comment: 'ECB policy play'
  },
  {
    positionId: 'POS_H003',
    symbol: 'USDCHF',
    type: 'SELL' as const,
    volume: 0.75,
    openPrice: 0.88964,
    closePrice: 0.89123,
    openTime: '2025-07-31T16:00:00Z',
    closeTime: '2025-08-01T08:20:00Z',
    pl: -119.25,
    swap: 0.40,
    commission: -7.50,
    comment: 'SNB intervention hedge'
  },
  {
    positionId: 'POS_H004',
    symbol: 'AUDUSD',
    type: 'BUY' as const,
    volume: 2.0,
    openPrice: 0.65089,
    closePrice: 0.65432,
    openTime: '2025-07-31T02:30:00Z',
    closeTime: '2025-07-31T18:45:00Z',
    pl: 686.00,
    swap: 1.60,
    commission: -20.00,
    comment: 'RBA rate decision'
  },
  {
    positionId: 'POS_H005',
    symbol: 'EURGBP',
    type: 'SELL' as const,
    volume: 1.5,
    openPrice: 0.83245,
    closePrice: 0.83189,
    openTime: '2025-07-30T11:15:00Z',
    closeTime: '2025-07-30T15:30:00Z',
    pl: 84.00,
    swap: -2.10,
    commission: -15.00,
    comment: 'Brexit sentiment trade'
  }
];

export const PositionHistory: React.FC = () => {
  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const formatPrice = (price: number, symbol: string) => {
    const decimals = symbol.includes('JPY') ? 3 : 5;
    return price.toFixed(decimals);
  };

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center space-x-2">
          <Calendar className="h-4 w-4 text-gray-500" />
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">
            Position History
          </h3>
          <span className="text-xs text-gray-500 dark:text-gray-400">
            ({mockPositionHistory.length} closed positions)
          </span>
        </div>
        <div className="flex items-center space-x-2 text-xs">
          <span className="text-gray-500 dark:text-gray-400">Filter:</span>
          <select className="bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded px-2 py-1 text-xs">
            <option>All Symbols</option>
            <option>EURUSD</option>
            <option>GBPUSD</option>
            <option>USDJPY</option>
          </select>
        </div>
      </div>

      {/* Table */}
      <div className="flex-1 overflow-auto">
        <table className="w-full text-xs">
          <thead className="bg-gray-50 dark:bg-gray-800 sticky top-0">
            <tr>
              <th className="px-3 py-2 text-left font-medium text-gray-700 dark:text-gray-300">Position</th>
              <th className="px-3 py-2 text-left font-medium text-gray-700 dark:text-gray-300">Symbol</th>
              <th className="px-3 py-2 text-left font-medium text-gray-700 dark:text-gray-300">Type</th>
              <th className="px-3 py-2 text-right font-medium text-gray-700 dark:text-gray-300">Volume</th>
              <th className="px-3 py-2 text-right font-medium text-gray-700 dark:text-gray-300">Open Price</th>
              <th className="px-3 py-2 text-right font-medium text-gray-700 dark:text-gray-300">Close Price</th>
              <th className="px-3 py-2 text-left font-medium text-gray-700 dark:text-gray-300">Open Time</th>
              <th className="px-3 py-2 text-left font-medium text-gray-700 dark:text-gray-300">Close Time</th>
              <th className="px-3 py-2 text-right font-medium text-gray-700 dark:text-gray-300">P&L</th>
              <th className="px-3 py-2 text-left font-medium text-gray-700 dark:text-gray-300">Comment</th>
            </tr>
          </thead>
          <tbody>
            {mockPositionHistory.map((position, index) => (
              <tr
                key={position.positionId}
                className={`border-b border-gray-100 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800/50 ${
                  index % 2 === 0 ? 'bg-white dark:bg-gray-900' : 'bg-gray-50/50 dark:bg-gray-800/30'
                }`}
              >
                <td className="px-3 py-2 font-mono text-gray-600 dark:text-gray-400">
                  {position.positionId}
                </td>
                <td className="px-3 py-2 font-semibold text-gray-900 dark:text-white">
                  {position.symbol}
                </td>
                <td className="px-3 py-2">
                  <span className={`px-1.5 py-0.5 rounded text-xs font-medium ${
                    position.type === 'BUY'
                      ? 'bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300'
                      : 'bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-300'
                  }`}>
                    {position.type}
                  </span>
                </td>
                <td className="px-3 py-2 text-right text-gray-900 dark:text-white">
                  {position.volume}
                </td>
                <td className="px-3 py-2 text-right font-mono text-gray-900 dark:text-white">
                  {formatPrice(position.openPrice, position.symbol)}
                </td>
                <td className="px-3 py-2 text-right font-mono text-gray-900 dark:text-white">
                  {formatPrice(position.closePrice, position.symbol)}
                </td>
                <td className="px-3 py-2 text-gray-600 dark:text-gray-400">
                  {formatDateTime(position.openTime)}
                </td>
                <td className="px-3 py-2 text-gray-600 dark:text-gray-400">
                  {formatDateTime(position.closeTime)}
                </td>
                <td className="px-3 py-2 text-right">
                  <div className="flex items-center justify-end space-x-1">
                    {position.pl >= 0 ? (
                      <TrendingUp className="h-3 w-3 text-green-500" />
                    ) : (
                      <TrendingDown className="h-3 w-3 text-red-500" />
                    )}
                    <span className={`font-semibold ${
                      position.pl >= 0 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {position.pl >= 0 ? '+' : ''}${position.pl.toFixed(2)}
                    </span>
                  </div>
                </td>
                <td className="px-3 py-2 text-gray-600 dark:text-gray-400 max-w-32 truncate">
                  {position.comment}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Summary Footer */}
      <div className="border-t border-gray-200 dark:border-gray-700 p-3 bg-gray-50 dark:bg-gray-800">
        <div className="flex justify-between items-center text-xs">
          <span className="text-gray-600 dark:text-gray-400">
            Total: {mockPositionHistory.length} positions
          </span>
          <div className="flex items-center space-x-4">
            <span className="text-green-600">
              Profitable: {mockPositionHistory.filter(p => p.pl > 0).length}
            </span>
            <span className="text-red-600">
              Loss: {mockPositionHistory.filter(p => p.pl < 0).length}
            </span>
            <span className="font-semibold text-gray-900 dark:text-white">
              Net P&L: ${mockPositionHistory.reduce((sum, p) => sum + p.pl, 0).toFixed(2)}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};