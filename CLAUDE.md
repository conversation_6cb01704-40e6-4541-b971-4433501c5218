# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

```bash
# Development
npm install          # Install dependencies
npm run dev          # Start dev server (auto-detects available port, typically 8080+)
npm run build        # Production build  
npm run build:dev    # Development build with dev mode
npm run lint         # Run ESLint (flat config with React Hooks + TypeScript)
npm run preview      # Preview production build locally

# Alternative package manager
bun install          # Alternative to npm install (lockfiles for both npm/bun available)

# Testing (currently no test scripts configured - add as needed)
# npm test            # Run tests (not yet configured)
# npm run test:watch  # Run tests in watch mode (not yet configured)
```

## Project Overview

**Nexus Trader Terminal** is a professional personal trading terminal built as a React single-page application. It provides real-time trading simulation, position management, advanced charting with technical analysis, and a **modern professional trading interface** for individual traders.

**Key Transformation**: This codebase was converted from "OTX Dealer Terminal Pro" (multi-account dealer management) to "Nexus Trader Terminal" (single personal trader account) with comprehensive KLineChart integration and **modern trade panel enhancements**.

## Architecture

### Core Stack
- **React 18.3.1** with TypeScript and **Vite 5.4.1** (React SWC plugin for fast compilation)
- **shadcn/ui** component library (40+ pre-built Radix UI components)
- **KLineChart v10.0.0-alpha5** for professional trading charts with technical indicators
- **TanStack React Query v5** for server state management and caching
- **React Router DOM v6** for client-side routing (simplified single route structure)
- **Tailwind CSS v3** with custom design system and dark theme
- **No form libraries currently used** (React Hook Form + Zod mentioned in old docs but not present)
- **No authentication system** (reCAPTCHA mentioned in old docs but not implemented)

### State Management Pattern
- **TradingContext** (`src/contexts/TradingContext.tsx`) - Centralized state for real-time currency pairs, symbol selection, and timeframe management
- **React Query** for server state caching, synchronization, and background updates
- **TypeScript interfaces** (`src/types/trading.ts`) - Comprehensive type definitions for single-account trading
- **Real-time Price Simulation** - Mock data with realistic market movements, volatility, and trend cycles

### Real-Time Data Management
The TradingContext provides comprehensive real-time market simulation:
- **Live Price Updates**: Currency pairs update every 1.5 seconds with realistic volatility patterns
- **Market Trend Simulation**: 5-minute trend cycles with different volatility for each currency type
- **Dynamic Spreads**: Spreads adjust based on market volatility and price movements
- **Professional Formatting**: JPY pairs display 3 decimals, other pairs show 5 decimals
- **Visual Feedback**: Price flash animations and color-coded movements (green up, red down)

### Key Components Structure
```
src/
├── components/
│   ├── ui/                    # shadcn/ui components (Button, Card, Input, Table, etc.)
│   ├── TradingHeader.tsx      # Header with Nexus branding and real-time account info
│   ├── MainChart.tsx          # Professional KLineChart with technical indicators and timeframe controls
│   ├── MiniChart.tsx          # Compact chart view with live price overlay
│   ├── Watchlist.tsx          # Currency pair list with real-time prices and selection
│   ├── OrderTicket.tsx        # **MODERN PROFESSIONAL TRADING INTERFACE**
│   │                          # • Risk-based position sizing with automatic calculation
│   │                          # • Real-time P&L analysis and risk/reward ratios
│   │                          # • Visual risk indicators and smart validation
│   │                          # • Professional order types (Market/Limit/Stop)
│   │                          # • Spread display and trading cost calculations
│   ├── MessagesPanel.tsx      # Trading notifications and alerts
│   ├── PositionsPanel.tsx     # Position management interface
│   ├── BottomPanel.tsx        # Tabbed interface for orders, positions, and activity
│   ├── DealHistory.tsx        # Deal history display
│   ├── OrderHistory.tsx       # Order history display
│   ├── PositionHistory.tsx    # Position history display
│   ├── TradePanel.tsx         # Trade management panel
│   ├── WalletPanel.tsx        # Wallet/account information panel
│   └── ErrorBoundary.tsx      # Error handling component
├── contexts/
│   └── TradingContext.tsx     # Centralized trading state and real-time data simulation
├── hooks/
│   ├── useKLineData.ts        # Custom hook for generating realistic candlestick data
│   ├── use-mobile.tsx         # Mobile device detection
│   └── use-toast.ts           # Toast notification system
├── pages/
│   └── Index.tsx              # Main application layout with three-column design
├── types/
│   └── trading.ts             # TypeScript interfaces for trading data structures
├── utils/
│   └── priceFormatting.ts     # Utility functions for price formatting and calculations
└── lib/
    └── utils.ts               # General utility functions (cn helper, etc.)
```

### Component Architecture Patterns
- **Three-Column Layout**: Fixed layout with left sidebar (Mini Chart, Watchlist, Order Ticket), center main chart, and right sidebar (Messages, Positions)
- **Real-time Data Flow**: All components automatically update from TradingContext state changes
- **Professional Chart Integration**: KLineChart with technical indicators, timeframe selection, and live price overlay
- **Interactive Watchlist**: Click-to-select currency pairs with price flash animations
- **Theme Support**: Built-in light/dark theme toggle with trading-optimized color schemes

### Chart Integration Architecture
- **MainChart Component**: Professional KLineChart integration with full-screen layout
- **MiniChart Component**: Compact chart view showing recent 20 candles with live price overlay
- **Chart Data Generation**: `useKLineData` hook creates realistic OHLC data from live prices
- **Technical Indicators**: MA, Bollinger Bands, RSI, MACD, Volume with interactive dropdown controls
- **Real-time Updates**: Charts automatically refresh with new price data every 1.5 seconds
- **Multiple Timeframes**: 1m, 5m, 15m, 30m, 1h, 4h, 1d with proper data aggregation
- **Interactive UI**: Timeframe buttons, indicator toggles, and live price display overlay

## Application Architecture

### Current Implementation Status
- **Main Layout**: Three-column desktop trading terminal design implemented in `src/pages/Index.tsx`
- **Authentication**: No authentication system currently implemented (direct access to trading interface)
- **Data Source**: Mock real-time simulation through TradingContext (no actual trading API integration)
- **Chart Library**: KLineChart v10.0.0-alpha5 integrated with professional trading styling
- **State Management**: React Context API for centralized trading state management

## Trading Terminal Features

### Currently Implemented Features
- **Live Price Display**: Real-time currency pair prices with EUR and USD sections in watchlist
- **Interactive Chart**: Professional KLineChart with technical indicators (MA, Bollinger Bands, RSI, MACD, Volume)
- **Symbol Selection**: Click-to-select currency pairs with visual feedback and price flash animations
- **Multiple Timeframes**: 1m, 5m, 15m, 30m, 1h, 4h, 1d chart views with live data
- **Mini Chart**: Compact 20-candle chart view with live price overlay
- **Theme Toggle**: Light/dark mode switching with trading-optimized color schemes
- **Professional UI**: Trading terminal styling with proper bid/ask color coding

### Mock Data Architecture
- **Realistic Price Simulation**: Different volatility patterns for different currency types (JPY, GBP, AUD have unique characteristics)
- **Market Trend Cycles**: 5-minute sine wave cycles create realistic trending behaviors
- **Dynamic Spreads**: Bid-ask spreads adjust based on volatility and market conditions
- **OHLC Generation**: `useKLineData` hook converts price ticks into proper candlestick data
- **Currency Pair Coverage**: 8 major pairs including EURUSD, GBPUSD, USDJPY, USDCHF, EURJPY, EURGBP, EURCHF, AUDUSD

## Development Patterns

### Component Development
- Use existing shadcn/ui components from `src/components/ui/`
- Follow single-account patterns (no multi-account complexity)
- Implement responsive design with Tailwind CSS utilities
- Utilize existing SortableTable component for data display
- Use TypeScript for all new components with proper type definitions

### Data Fetching and State Management
- **TradingContext**: Centralized state management for currency pairs, selected symbol, and timeframe
- **Real-time Updates**: useEffect hooks manage price simulation intervals and automatic updates
- **Chart Data Flow**: `useKLineData` hook generates candlestick data from real-time prices
- **Visual Feedback**: Price flash animations and color transitions managed through component state

### Development Workflow
- **Mock Data System**: Real-time price simulation runs automatically on component mount
- **Hot Reload**: Vite development server with fast refresh for efficient development
- **Theme Testing**: Built-in theme toggle allows testing both light and dark modes
- **Chart Development**: KLineChart integration with live data updates for testing indicators
- **Component Structure**: Three-column layout makes it easy to develop and test individual panels

### Application Routing Structure
```
/ (root)     → Direct access to trading terminal (no authentication required)
```

**Current Flow**: Single-page application with immediate access to trading interface

### Styling Conventions
- **Tailwind CSS**: Custom trading color scheme with professional bid/ask color coding
- **Theme Support**: Light/dark themes with trading-optimized contrast levels
- **Responsive Design**: Fixed three-column layout optimized for desktop trading
- **Professional Branding**: "Nexus Trader Terminal" styling with consistent typography
- **Trading Colors**: Custom trading color palette in tailwind.config.ts including trading-specific colors

## Configuration Files

- **TypeScript**: Multi-project setup with `tsconfig.json` (base), `tsconfig.app.json`, `tsconfig.node.json`
  - Path aliases (`@/*` → `./src/*`) configured for clean imports
  - Relaxed strict mode settings for development flexibility (noImplicitAny: false, strictNullChecks: false)
- **ESLint**: Flat config (`eslint.config.js`) with React Hooks and TypeScript integration
  - React Refresh plugin for hot reloading in development
  - Unused variables disabled for development workflow (@typescript-eslint/no-unused-vars: "off")
- **Vite**: React SWC plugin for fast compilation, dev server auto-port detection (port 8080)
  - Lovable.dev component tagger integration for development mode only
  - Path alias resolution matches TypeScript configuration
- **Tailwind CSS**: Custom design system with CSS variables, dark mode support, and trading-specific color palette
- **PostCSS**: Autoprefixer for cross-browser compatibility

## Environment Setup

1. **Install dependencies**: `npm install` (or `bun install`)
2. **Start development**: `npm run dev`
3. **Access application**: http://localhost:8080 (or auto-detected port)
4. **No authentication required**: Direct access to trading terminal interface

## Important Implementation Notes

### Current Architecture Overview
- **Layout Design**: Three-column fixed layout designed for desktop trading workstations
- **Real-time Simulation**: Mock market data with realistic price movements and volatility patterns
- **Chart Integration**: Professional KLineChart library with technical indicators and timeframe controls
- **State Management**: React Context API provides centralized trading state across all components
- **No Authentication**: Direct access trading terminal without login requirements

### Key Technical Components
- **TradingContext**: Central hub for price data, symbol selection, and real-time updates
- **useKLineData Hook**: Converts live price ticks into proper OHLC candlestick data
- **Price Flash Animations**: Visual feedback system for price movements with color-coded changes
- **Responsive Indicators**: Technical analysis tools (MA, Bollinger Bands, RSI, MACD) with interactive controls

### Development Focus Areas
- All components designed for professional trading interface aesthetics
- Real-time data simulation provides realistic market behavior for development/testing
- Component architecture supports easy integration of actual trading APIs in the future
- Visual feedback systems provide professional trading terminal user experience
- No external dependencies for authentication or real trading APIs - fully self-contained simulation system