/**
 * Utility functions for consistent price formatting across the application
 * based on trading symbol types and their appropriate decimal precision.
 */

/**
 * Formats a price value with the appropriate decimal precision based on the trading symbol.
 * 
 * @param price - The price value to format
 * @param symbol - The trading symbol (e.g., 'EURUSD', 'XAUUSD', 'BTCUSD')
 * @returns Formatted price string with appropriate decimal places
 */
export const formatPrice = (price: number | string, symbol: string): string => {
  const numericPrice = typeof price === 'string' ? parseFloat(price) : price;
  
  if (isNaN(numericPrice)) {
    return '—';
  }
  
  if (symbol.includes('XAU')) {
    // Gold (XAUUSD) - 2 decimal places
    return numericPrice.toFixed(2);
  } else if (symbol.includes('XAG')) {
    // Silver (XAGUSD) - 3 decimal places
    return numericPrice.toFixed(3);
  } else if (symbol.includes('BTC') || symbol.includes('ETH')) {
    // Cryptocurrencies (BTCUSD, ETHUSD) - 1 decimal place
    return numericPrice.toFixed(1);
  } else if (symbol.includes('JPY')) {
    // JPY pairs (USDJPY, EURJPY, GBPJPY) - 3 decimal places
    return numericPrice.toFixed(3);
  } else {
    // Standard forex pairs (EURUSD, GBPUSD, etc.) - 5 decimal places
    return numericPrice.toFixed(5);
  }
};

/**
 * Gets the appropriate decimal precision for a given trading symbol.
 * 
 * @param symbol - The trading symbol
 * @returns Number of decimal places appropriate for the symbol
 */
export const getSymbolPrecision = (symbol: string): number => {
  if (symbol.includes('XAU')) {
    return 2; // Gold
  } else if (symbol.includes('XAG')) {
    return 3; // Silver
  } else if (symbol.includes('BTC') || symbol.includes('ETH')) {
    return 1; // Cryptocurrencies
  } else if (symbol.includes('JPY')) {
    return 3; // JPY pairs
  } else {
    return 5; // Standard forex
  }
};

/**
 * Gets the appropriate step value for HTML input fields based on trading symbol precision.
 * 
 * @param symbol - The trading symbol
 * @returns Step value as string for HTML input step attribute
 */
export const getSymbolStep = (symbol: string): string => {
  const precision = getSymbolPrecision(symbol);
  
  if (precision === 1) {
    return "0.1";
  } else if (precision === 2) {
    return "0.01";
  } else if (precision === 3) {
    return "0.001";
  } else if (precision === 5) {
    return "0.00001";
  } else {
    return "0.00001"; // Default fallback
  }
};

/**
 * Formats a price value or returns a fallback string if the value is null/undefined.
 * 
 * @param price - The price value to format (can be null/undefined)
 * @param symbol - The trading symbol
 * @param fallback - The fallback string to return if price is null/undefined (default: '—')
 * @returns Formatted price string or fallback
 */
export const formatPriceOrFallback = (
  price: number | string | null | undefined, 
  symbol: string, 
  fallback: string = '—'
): string => {
  if (price === null || price === undefined || price === '') {
    return fallback;
  }
  return formatPrice(price, symbol);
}; 