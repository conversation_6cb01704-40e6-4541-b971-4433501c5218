import React from 'react';
import { useTradingContext } from '../contexts/TradingContext';
import { Card } from './ui/card';
import { Wallet, TrendingUp, AlertTriangle, PieChart } from 'lucide-react';

export const WalletPanel: React.FC = () => {
  const { accountSummary } = useTradingContext();

  // Calculate margin utilization percentage
  const marginUtilization = (accountSummary.usedMargin / accountSummary.equity) * 100;
  const marginLevel = (accountSummary.equity / accountSummary.usedMargin) * 100;

  // Determine margin health status
  const getMarginStatus = () => {
    if (marginLevel > 500) return { color: 'text-green-600', text: 'Healthy', icon: TrendingUp };
    if (marginLevel > 200) return { color: 'text-yellow-600', text: 'Moderate', icon: AlertTriangle };
    return { color: 'text-red-600', text: 'High Risk', icon: AlertTriangle };
  };

  const marginStatus = getMarginStatus();
  const MarginIcon = marginStatus.icon;

  return (
    <div className="h-full flex flex-col bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700">
      {/* Header */}
      <div className="p-3 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center space-x-2">
          <Wallet className="h-4 w-4 text-gray-600 dark:text-gray-400" />
          <h3 className="text-sm font-semibold text-gray-900 dark:text-white">
            Wallet
          </h3>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 p-3 space-y-3 overflow-y-auto">
        {/* Balance Overview */}
        <Card className="p-3 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20">
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-xs text-gray-600 dark:text-gray-400">Balance</span>
              <span className="text-sm font-semibold text-gray-900 dark:text-white">
                ${accountSummary.balance.toLocaleString()}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-xs text-gray-600 dark:text-gray-400">Equity</span>
              <span className="text-sm font-semibold text-green-600">
                ${accountSummary.equity.toLocaleString()}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-xs text-gray-600 dark:text-gray-400">Floating P&L</span>
              <span className={`text-sm font-semibold ${
                accountSummary.fpl >= 0 ? 'text-green-600' : 'text-red-600'
              }`}>
                {accountSummary.fpl >= 0 ? '+' : ''}${accountSummary.fpl.toFixed(2)}
              </span>
            </div>
          </div>
        </Card>

        {/* Margin Information */}
        <Card className="p-3">
          <div className="space-y-2">
            <div className="flex items-center space-x-2 mb-2">
              <PieChart className="h-3 w-3 text-gray-500" />
              <span className="text-xs font-medium text-gray-700 dark:text-gray-300">
                Margin Status
              </span>
              <div className={`flex items-center space-x-1 ${marginStatus.color}`}>
                <MarginIcon className="h-3 w-3" />
                <span className="text-xs font-medium">{marginStatus.text}</span>
              </div>
            </div>
            
            {/* Margin utilization bar */}
            <div className="space-y-1">
              <div className="flex justify-between text-xs">
                <span className="text-gray-600 dark:text-gray-400">Used</span>
                <span className="text-gray-600 dark:text-gray-400">
                  ${accountSummary.usedMargin.toLocaleString()}
                </span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div 
                  className={`h-2 rounded-full transition-all duration-300 ${
                    marginUtilization > 80 ? 'bg-red-500' :
                    marginUtilization > 60 ? 'bg-yellow-500' : 'bg-green-500'
                  }`}
                  style={{ width: `${Math.min(marginUtilization, 100)}%` }}
                />
              </div>
              <div className="flex justify-between text-xs">
                <span className="text-gray-500 dark:text-gray-400">
                  {marginUtilization.toFixed(1)}% used
                </span>
                <span className="text-gray-500 dark:text-gray-400">
                  Free: ${accountSummary.usableMargin.toLocaleString()}
                </span>
              </div>
            </div>
          </div>
        </Card>

        {/* Account Info */}
        <Card className="p-3">
          <div className="space-y-2">
            <h4 className="text-xs font-medium text-gray-700 dark:text-gray-300">
              Account Details
            </h4>
            <div className="space-y-1.5 text-xs">
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Account</span>
                <span className="text-gray-900 dark:text-white font-mono">
                  {accountSummary.account}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Currency</span>
                <span className="text-gray-900 dark:text-white">
                  {accountSummary.currency}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">User</span>
                <span className="text-gray-900 dark:text-white">
                  {accountSummary.user}
                </span>
              </div>
            </div>
          </div>
        </Card>

        {/* Quick Actions */}
        <div className="space-y-2 pt-1">
          <button className="w-full px-3 py-2 text-xs bg-green-50 hover:bg-green-100 dark:bg-green-900/20 dark:hover:bg-green-900/30 text-green-600 dark:text-green-400 rounded-md transition-colors border border-green-200 dark:border-green-800">
            Deposit Funds
          </button>
          <button className="w-full px-3 py-2 text-xs bg-gray-50 hover:bg-gray-100 dark:bg-gray-800 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-400 rounded-md transition-colors border border-gray-200 dark:border-gray-700">
            Withdraw
          </button>
        </div>
      </div>
    </div>
  );
};