import React, { useState, useEffect, useMemo } from 'react';
import { useTradingContext } from '../contexts/TradingContext';

export const OrderTicket: React.FC = () => {
  const { selectedSymbol, getCurrentPair } = useTradingContext();
  const [volume, setVolume] = useState('0.01');
  const [orderType, setOrderType] = useState<'market' | 'limit' | 'stop'>('market');
  const [limitPrice, setLimitPrice] = useState('');
  const [stopLoss, setStopLoss] = useState('');
  const [takeProfit, setTakeProfit] = useState('');
  const [tpSlEnabled, setTpSlEnabled] = useState(false);
  const [riskPercent, setRiskPercent] = useState('2');
  const [riskAmount, setRiskAmount] = useState('');
  const [useRiskCalculator, setUseRiskCalculator] = useState(false);
  const [positionSizeMode, setPositionSizeMode] = useState<'manual' | 'risk'>('manual');

  const currentPair = getCurrentPair();
  const volumeOptions = ['0.01', '0.1', '1', '10'];
  
  // Mock account data - in real app this would come from context/API
  const accountBalance = 10000; // USD
  const accountEquity = 10000; // USD
  
  // Calculate position size based on risk management
  const calculatedVolume = useMemo(() => {
    if (!useRiskCalculator || !stopLoss || !currentPair) return null;
    
    const riskAmountValue = parseFloat(riskAmount) || (accountEquity * parseFloat(riskPercent)) / 100;
    const stopLossPrice = parseFloat(stopLoss);
    const currentPrice = orderType === 'market' ? currentPair.ask : parseFloat(limitPrice) || currentPair.ask;
    
    if (stopLossPrice <= 0 || currentPrice <= 0) return null;
    
    const pipValue = currentPair.symbol.includes('JPY') ? 0.01 : 0.0001;
    const stopDistance = Math.abs(currentPrice - stopLossPrice);
    const pipsAtRisk = stopDistance / pipValue;
    
    if (pipsAtRisk <= 0) return null;
    
    // For forex: Risk Amount / (Pips at Risk * Pip Value * Position Size) = Position Size
    // Simplified calculation for demo purposes
    const calculatedLots = riskAmountValue / (pipsAtRisk * 10); // 10 USD per pip for standard lot
    
    return Math.max(0.01, Math.round(calculatedLots * 100) / 100);
  }, [useRiskCalculator, riskAmount, riskPercent, stopLoss, currentPair, orderType, limitPrice, accountEquity]);
  
  // Auto-update volume when using risk calculator
  useEffect(() => {
    if (useRiskCalculator && calculatedVolume) {
      setVolume(calculatedVolume.toString());
    }
  }, [calculatedVolume, useRiskCalculator]);

  // Validation logic
  const validation = useMemo(() => {
    const errors: string[] = [];
    const warnings: string[] = [];
    
    const finalVolume = parseFloat(volume) || 0;
    
    // Volume validation
    if (finalVolume <= 0) {
      errors.push('Volume must be greater than 0');
    }
    if (finalVolume > accountEquity / 1000) {
      warnings.push('Position size may be too large for account');
    }
    
    // Price validation for limit/stop orders
    if (orderType !== 'market' && (!limitPrice || parseFloat(limitPrice) <= 0)) {
      errors.push('Price is required for limit/stop orders');
    }
    
    // TP/SL validation
    if (tpSlEnabled) {
      if (stopLoss && parseFloat(stopLoss) <= 0) {
        errors.push('Stop Loss must be greater than 0');
      }
      if (takeProfit && parseFloat(takeProfit) <= 0) {
        errors.push('Take Profit must be greater than 0');
      }
    }
    
    // Risk validation
    if (positionSizeMode === 'risk') {
      const risk = parseFloat(riskPercent);
      if (risk > 10) {
        errors.push('Risk percentage cannot exceed 10%');
      }
      if (risk > 5) {
        warnings.push('Risk percentage above 5% is not recommended');
      }
    }
    
    return { errors, warnings, isValid: errors.length === 0 };
  }, [volume, orderType, limitPrice, stopLoss, takeProfit, tpSlEnabled, 
      positionSizeMode, riskPercent, accountEquity]);

  const estimatedPL = useMemo(() => {
    if (!currentPair || !tpSlEnabled) return null;
    
    const finalVolume = parseFloat(volume) || 0;
    const currentPrice = orderType === 'market' ? currentPair.ask : parseFloat(limitPrice) || currentPair.ask;
    const slPrice = parseFloat(stopLoss) || 0;
    const tpPrice = parseFloat(takeProfit) || 0;
    
    if (finalVolume <= 0 || currentPrice <= 0) return null;
    
    const pipValue = currentPair.symbol.includes('JPY') ? 0.01 : 0.0001;
    const pipValueUSD = 10 * finalVolume; // Simplified: $10 per pip per standard lot
    
    let maxLoss = null;
    let maxProfit = null;
    
    if (slPrice > 0) {
      const slPips = Math.abs(currentPrice - slPrice) / pipValue;
      maxLoss = -(slPips * pipValueUSD);
    }
    
    if (tpPrice > 0) {
      const tpPips = Math.abs(tpPrice - currentPrice) / pipValue;
      maxProfit = tpPips * pipValueUSD;
    }
    
    const riskReward = maxLoss && maxProfit ? Math.abs(maxProfit / maxLoss) : null;
    
    return { maxLoss, maxProfit, riskReward };
  }, [currentPair, volume, orderType, limitPrice, stopLoss, takeProfit, tpSlEnabled]);

  const handleBuy = () => {
    console.log('Buy order:', { 
      symbol: selectedSymbol, 
      volume: volume, 
      type: orderType,
      stopLoss: tpSlEnabled ? stopLoss : undefined,
      takeProfit: tpSlEnabled ? takeProfit : undefined,
      estimatedPL
    });
  };

  const handleSell = () => {
    console.log('Sell order:', { 
      symbol: selectedSymbol, 
      volume: volume, 
      type: orderType,
      stopLoss: tpSlEnabled ? stopLoss : undefined,
      takeProfit: tpSlEnabled ? takeProfit : undefined,
      estimatedPL
    });
  };

  return (
    <div className="flex-1 bg-trading-panel p-trading overflow-hidden">
      <div className="h-full flex flex-col">
        {/* Symbol Info */}
        <div className="mb-trading">
          <div className="text-sm font-medium text-trading-text font-mono">
            {selectedSymbol}
          </div>
          <div className="text-xs text-trading-text-muted mt-1">
            {currentPair?.name}
          </div>
        </div>

        {/* Order Type Selection */}
        <div className="mb-trading">
          <div className="flex rounded-trading border border-trading-border overflow-hidden">
            {(['market', 'limit', 'stop'] as const).map((type) => (
              <button
                key={type}
                onClick={() => setOrderType(type)}
                className={`flex-1 py-2 px-3 text-xs font-medium uppercase tracking-wide transition-all ${
                  orderType === type
                    ? 'bg-trading-accent text-white'
                    : 'bg-trading-bg-darker text-trading-text-muted hover:bg-trading-border hover:text-trading-text'
                }`}
              >
                {type}
              </button>
            ))}
          </div>
        </div>

        {/* Order Form */}
        <div className="flex-1 space-y-trading">
          {/* Position Size Mode Toggle */}
          <div className="flex rounded-trading border border-trading-border overflow-hidden mb-trading">
            <button
              onClick={() => setPositionSizeMode('manual')}
              className={`flex-1 py-2 px-3 text-xs font-medium uppercase tracking-wide transition-all ${
                positionSizeMode === 'manual'
                  ? 'bg-trading-accent text-white'
                  : 'bg-trading-bg-darker text-trading-text-muted hover:bg-trading-border hover:text-trading-text'
              }`}
            >
              Manual
            </button>
            <button
              onClick={() => {
                setPositionSizeMode('risk');
                setUseRiskCalculator(true);
              }}
              className={`flex-1 py-2 px-3 text-xs font-medium uppercase tracking-wide transition-all ${
                positionSizeMode === 'risk'
                  ? 'bg-trading-accent text-white'
                  : 'bg-trading-bg-darker text-trading-text-muted hover:bg-trading-border hover:text-trading-text'
              }`}
            >
              Risk-Based
            </button>
          </div>

          {/* Risk Calculator Section */}
          {positionSizeMode === 'risk' && (
            <div className="p-trading-sm bg-trading-bg-darker rounded-trading border border-trading-border space-y-2">
              <div className="text-xs font-medium text-trading-text-muted uppercase tracking-wide mb-2">
                Risk Management
              </div>
              
              {/* Account Info */}
              <div className="flex justify-between text-xs">
                <span className="text-trading-text-dim">Balance:</span>
                <span className="font-mono text-trading-text">${accountBalance.toLocaleString()}</span>
              </div>
              <div className="flex justify-between text-xs">
                <span className="text-trading-text-dim">Equity:</span>
                <span className="font-mono text-trading-text">${accountEquity.toLocaleString()}</span>
              </div>
              
              {/* Risk Input */}
              <div className="flex space-x-2">
                <div className="flex-1">
                  <label className="block text-xs font-medium text-trading-text-muted mb-1">Risk %</label>
                  <input
                    type="number"
                    value={riskPercent}
                    onChange={(e) => setRiskPercent(e.target.value)}
                    className="w-full px-2 py-1 text-xs font-mono border border-trading-border rounded-trading focus:ring-1 focus:ring-trading-accent bg-trading-bg-darker text-trading-text"
                    step="0.1"
                    min="0.1"
                    max="10"
                  />
                </div>
                <div className="flex-1">
                  <label className="block text-xs font-medium text-trading-text-muted mb-1">Risk $</label>
                  <input
                    type="number"
                    value={riskAmount}
                    onChange={(e) => setRiskAmount(e.target.value)}
                    className="w-full px-2 py-1 text-xs font-mono border border-trading-border rounded-trading focus:ring-1 focus:ring-trading-accent bg-trading-bg-darker text-trading-text"
                    placeholder={((accountEquity * parseFloat(riskPercent)) / 100).toFixed(0)}
                  />
                </div>
              </div>
              
              {/* Calculated Volume Display */}
              {calculatedVolume && (
                <div className="flex justify-between text-xs">
                  <span className="text-trading-text-dim">Calculated Size:</span>
                  <span className="font-mono text-trading-accent font-medium">{calculatedVolume} lots</span>
                </div>
              )}
              
              {/* Risk Warnings */}
              {parseFloat(riskPercent) > 5 && (
                <div className="flex items-center space-x-1 text-xs text-trading-down bg-trading-down/10 px-2 py-1 rounded">
                  <span className="w-2 h-2 bg-trading-down rounded-full"></span>
                  <span>High Risk: {riskPercent}% exceeds recommended 2-5%</span>
                </div>
              )}
              
              {calculatedVolume && calculatedVolume > 10 && (
                <div className="flex items-center space-x-1 text-xs text-yellow-500 bg-yellow-500/10 px-2 py-1 rounded">
                  <span className="w-2 h-2 bg-yellow-500 rounded-full"></span>
                  <span>Large Position: {calculatedVolume} lots is a significant size</span>
                </div>
              )}
            </div>
          )}

          {/* Volume Selection */}
          <div>
            <label className="block text-xs font-medium text-trading-text-muted mb-1 uppercase tracking-wide">
              {positionSizeMode === 'risk' ? 'Position Size (Auto-calculated)' : 'Volume'}
            </label>
            {/* Quick volume buttons */}
            <div className="flex flex-wrap gap-1 mb-2">
              {volumeOptions.map((vol) => (
                <button
                  key={vol}
                  onClick={() => setVolume(vol)}
                  className={`px-3 py-1 text-xs font-mono rounded-trading border transition-all ${
                    volume === vol
                      ? 'bg-trading-accent border-trading-accent text-white'
                      : 'bg-trading-bg-darker border-trading-border text-trading-text-muted hover:border-trading-accent hover:text-trading-text'
                  }`}
                >
                  {vol}
                </button>
              ))}
            </div>
            {/* Volume input box - always visible */}
            <input
              type="number"
              value={volume}
              onChange={(e) => setVolume(e.target.value)}
              className="w-full px-3 py-2 text-sm font-mono border border-trading-border rounded-trading focus:ring-1 focus:ring-trading-accent bg-trading-bg-darker text-trading-text placeholder-trading-text-dim"
              step="0.01"
              min="0.01"
              placeholder="Enter volume"
            />
          </div>

          {/* Limit/Stop Price (only for limit/stop orders) */}
          {orderType !== 'market' && (
            <div>
              <label className="block text-xs font-medium text-trading-text-muted mb-1 uppercase tracking-wide">
                Price
              </label>
              <input
                type="number"
                value={limitPrice}
                onChange={(e) => setLimitPrice(e.target.value)}
                className="w-full px-3 py-2 text-sm font-mono border border-trading-border rounded-trading focus:ring-1 focus:ring-trading-accent bg-trading-bg-darker text-trading-text placeholder-trading-text-dim"
                step="0.00001"
              />
            </div>
          )}

          {/* TP/SL Toggle */}
          <div className="flex items-center justify-between py-2">
            <span className="text-xs font-medium text-trading-text-muted uppercase tracking-wide">
              Enable TP/SL
            </span>
            <button
              onClick={() => setTpSlEnabled(!tpSlEnabled)}
              className={`relative inline-flex h-5 w-9 items-center rounded-full transition-colors ${
                tpSlEnabled ? 'bg-trading-accent' : 'bg-trading-border'
              }`}
            >
              <span
                className={`inline-block h-3 w-3 transform rounded-full bg-white transition-transform ${
                  tpSlEnabled ? 'translate-x-5' : 'translate-x-1'
                }`}
              />
            </button>
          </div>

          {/* TP/SL Inputs (only when enabled) */}
          {tpSlEnabled && (
            <div className="space-y-trading">
              {/* Stop Loss */}
              <div>
                <label className="block text-xs font-medium text-trading-text-muted mb-1 uppercase tracking-wide">
                  Stop Loss
                </label>
                <input
                  type="number"
                  value={stopLoss}
                  onChange={(e) => setStopLoss(e.target.value)}
                  className="w-full px-3 py-2 text-sm font-mono border border-trading-border rounded-trading focus:ring-1 focus:ring-trading-accent bg-trading-bg-darker text-trading-text placeholder-trading-text-dim"
                  step="0.00001"
                  placeholder="Optional"
                />
              </div>

              {/* Take Profit */}
              <div>
                <label className="block text-xs font-medium text-trading-text-muted mb-1 uppercase tracking-wide">
                  Take Profit
                </label>
                <input
                  type="number"
                  value={takeProfit}
                  onChange={(e) => setTakeProfit(e.target.value)}
                  className="w-full px-3 py-2 text-sm font-mono border border-trading-border rounded-trading focus:ring-1 focus:ring-trading-accent bg-trading-bg-darker text-trading-text placeholder-trading-text-dim"
                  step="0.00001"
                  placeholder="Optional"
                />
              </div>
            </div>
          )}
        </div>

        {/* Trading Information Panel */}
        {currentPair && estimatedPL && (
          <div className="mb-trading p-trading-sm bg-trading-bg-darker rounded-trading border border-trading-border space-y-2">
            {/* Estimated P&L Display */}
            <>
              <div className="text-xs font-medium text-trading-text-muted uppercase tracking-wide mb-2">
                Risk/Reward Analysis
              </div>
              
              {estimatedPL.maxLoss && (
                <div className="flex justify-between text-xs">
                  <span className="text-trading-text-dim">Max Loss:</span>
                  <span className="font-mono text-trading-down font-medium">
                    ${Math.abs(estimatedPL.maxLoss).toFixed(2)}
                  </span>
                </div>
              )}
              
              {estimatedPL.maxProfit && (
                <div className="flex justify-between text-xs">
                  <span className="text-trading-text-dim">Max Profit:</span>
                  <span className="font-mono text-trading-up font-medium">
                    ${estimatedPL.maxProfit.toFixed(2)}
                  </span>
                </div>
              )}
              
              {estimatedPL.riskReward && (
                <div className="flex justify-between text-xs">
                  <span className="text-trading-text-dim">Risk/Reward:</span>
                  <span className={`font-mono font-medium ${
                    estimatedPL.riskReward >= 2 ? 'text-trading-up' : 
                    estimatedPL.riskReward >= 1 ? 'text-trading-accent' : 'text-trading-down'
                  }`}>
                    1:{estimatedPL.riskReward.toFixed(1)}
                  </span>
                </div>
              )}
              
              {/* Risk/Reward Warnings */}
              {estimatedPL.riskReward && estimatedPL.riskReward < 1 && (
                <div className="flex items-center space-x-1 text-xs text-trading-down bg-trading-down/10 px-2 py-1 rounded mt-2">
                  <span className="w-2 h-2 bg-trading-down rounded-full"></span>
                  <span>Poor R:R - Risk exceeds potential reward</span>
                </div>
              )}
              
              {estimatedPL.riskReward && estimatedPL.riskReward >= 3 && (
                <div className="flex items-center space-x-1 text-xs text-trading-up bg-trading-up/10 px-2 py-1 rounded mt-2">
                  <span className="w-2 h-2 bg-trading-up rounded-full"></span>
                  <span>Excellent R:R - Strong risk management</span>
                </div>
              )}
            </>
          </div>
        )}

        {/* Validation Messages */}
        {(validation.errors.length > 0 || validation.warnings.length > 0) && (
          <div className="space-y-1">
            {validation.errors.map((error, index) => (
              <div key={`error-${index}`} className="flex items-center space-x-1 text-xs text-trading-down bg-trading-down/10 px-2 py-1 rounded">
                <span className="w-2 h-2 bg-trading-down rounded-full"></span>
                <span>{error}</span>
              </div>
            ))}
            {validation.warnings.map((warning, index) => (
              <div key={`warning-${index}`} className="flex items-center space-x-1 text-xs text-yellow-500 bg-yellow-500/10 px-2 py-1 rounded">
                <span className="w-2 h-2 bg-yellow-500 rounded-full"></span>
                <span>{warning}</span>
              </div>
            ))}
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex space-x-2 mt-trading">
          <button
            onClick={handleSell}
            disabled={!validation.isValid}
            className={`flex-1 text-white text-sm font-medium py-3 px-4 rounded-trading transition-all shadow-trading ${
              validation.isValid 
                ? 'bg-trading-down hover:bg-trading-down/80' 
                : 'bg-gray-400 cursor-not-allowed opacity-50'
            }`}
          >
            SELL
          </button>
          <button
            onClick={handleBuy}
            disabled={!validation.isValid}
            className={`flex-1 text-white text-sm font-medium py-3 px-4 rounded-trading transition-all shadow-trading ${
              validation.isValid 
                ? 'bg-trading-up hover:bg-trading-up/80' 
                : 'bg-gray-400 cursor-not-allowed opacity-50'
            }`}
          >
            BUY
          </button>
        </div>
      </div>
    </div>
  );
};