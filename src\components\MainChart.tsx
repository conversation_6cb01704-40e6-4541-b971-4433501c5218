import React, { useEffect, useRef, useState } from 'react';
import { init, dispose } from 'klinecharts';
import type { Chart } from 'klinecharts';
import { useKLineData } from '../hooks/useKLineData';
import { useTradingContext } from '../contexts/TradingContext';
import { ChevronDown, BarChart3, TrendingUp, Settings, Plus } from 'lucide-react';

export const MainChart: React.FC = () => {
  const { selectedSymbol, selectedTimeframe, setSelectedTimeframe, getCurrentPair } = useTradingContext();
  const currentPair = getCurrentPair();
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstanceRef = useRef<Chart | null>(null);
  const [showIndicators, setShowIndicators] = useState(false);
  
  // Get mock candlestick data
  const klineData = useKLineData(selectedSymbol, selectedTimeframe);

  const timeframes = [
    { id: '1m', label: '1m' },
    { id: '5m', label: '5m' },
    { id: '15m', label: '15m' },
    { id: '30m', label: '30m' },
    { id: '1h', label: '1h' },
    { id: '4h', label: '4h' },
    { id: '1d', label: '1d' },
  ];

  const indicators = [
    { id: 'MA', label: 'Moving Average', enabled: true },
    { id: 'BOLL', label: 'Bollinger Bands', enabled: true },
    { id: 'RSI', label: 'RSI', enabled: true },
    { id: 'MACD', label: 'MACD', enabled: false },
    { id: 'VOL', label: 'Volume', enabled: true },
  ];

  const toggleIndicator = (indicatorId: string) => {
    if (chartInstanceRef.current) {
      const chart = chartInstanceRef.current;
      const currentIndicators = chart.getIndicators() || [];
      const hasIndicator = currentIndicators.some(ind => ind.name === indicatorId);
      
      if (hasIndicator) {
        chart.removeIndicator(indicatorId);
      } else {
        if (indicatorId === 'VOL') {
          chart.createIndicator(indicatorId, true, { id: 'candle_pane' });
        } else if (indicatorId === 'MA' || indicatorId === 'BOLL') {
          chart.createIndicator(indicatorId, false, { id: 'candle_pane' });
        } else {
          chart.createIndicator(indicatorId, true);
        }
      }
    }
  };

  // Initialize chart
  useEffect(() => {
    if (!chartRef.current) return;

    // Create chart instance
    const chart = init(chartRef.current);
    chartInstanceRef.current = chart;

    // Configure chart styles for professional trading look
    chart.setStyles({
      grid: {
        show: true,
        horizontal: {
          show: true,
          size: 1,
          color: '#333333',
          style: 'solid'
        },
        vertical: {
          show: true,
          size: 1,
          color: '#333333',
          style: 'solid'
        }
      },
      candle: {
        type: 'candle_solid',
        bar: {
          upColor: '#00ff88',
          downColor: '#ff4757',
          noChangeColor: '#a3a3a3',
        },
        tooltip: {
          showRule: 'always',
          showType: 'standard',
          title: {
            show: true,
            size: 14,
            family: 'Inter',
            weight: 'normal',
            color: '#ffffff',
            marginLeft: 8,
            marginTop: 4,
            marginRight: 8,
            marginBottom: 4,
            template: '{ticker} · {period}'
          },
          legend: {
            size: 12,
            family: 'JetBrains Mono',
            weight: 'normal',
            color: '#a3a3a3',
            marginLeft: 8,
            marginTop: 4,
            marginRight: 8,
            marginBottom: 4,
            defaultValue: 'n/a',
            template: [
              { title: 'Time', value: '{time}' },
              { title: 'O', value: '{open}' },
              { title: 'H', value: '{high}' },
              { title: 'L', value: '{low}' },
              { title: 'C', value: '{close}' },
              { title: 'Vol', value: '{volume}' }
            ]
          }
        }
      },
      xAxis: {
        show: true,
        height: 50,
        axisLine: {
          show: true,
          color: '#333333',
          size: 1
        },
        tickText: {
          show: true,
          color: '#a3a3a3',
          size: 11,
          family: 'Inter',
          weight: 'normal'
        },
        tickLine: {
          show: true,
          size: 1,
          length: 3,
          color: '#333333'
        }
      },
      yAxis: {
        show: true,
        width: 80,
        position: 'right',
        type: 'normal',
        inside: false,
        reverse: false,
        axisLine: {
          show: true,
          color: '#333333',
          size: 1
        },
        tickText: {
          show: true,
          color: '#a3a3a3',
          size: 11,
          family: 'JetBrains Mono',
          weight: 'normal'
        },
        tickLine: {
          show: true,
          size: 1,
          length: 5,
          color: '#333333'
        }
      },
      crosshair: {
        show: true,
        horizontal: {
          show: true,
          line: {
            show: true,
            style: 'dashed',
            dashedValue: [4, 2],
            size: 1,
            color: '#404040'
          },
          text: {
            show: true,
            color: '#ffffff',
            backgroundColor: '#1a1a1a',
            size: 11,
            family: 'JetBrains Mono',
            weight: 'normal',
            paddingLeft: 4,
            paddingRight: 4,
            paddingTop: 2,
            paddingBottom: 2,
            borderRadius: 2,
            borderSize: 1,
            borderColor: '#333333'
          }
        },
        vertical: {
          show: true,
          line: {
            show: true,
            style: 'dashed',
            dashedValue: [4, 2],
            size: 1,
            color: '#404040'
          },
          text: {
            show: true,
            color: '#ffffff',
            backgroundColor: '#1a1a1a',
            size: 11,
            family: 'Inter',
            weight: 'normal',
            paddingLeft: 4,
            paddingRight: 4,
            paddingTop: 2,
            paddingBottom: 2,
            borderRadius: 2,
            borderSize: 1,
            borderColor: '#333333'
          }
        }
      },
      indicator: {
        tooltip: {
          showRule: 'always',
          showType: 'standard',
          title: {
            show: true,
            showName: true,
            showParams: true,
            size: 12,
            family: 'Inter',
            weight: 'normal',
            color: '#a3a3a3',
            marginLeft: 8,
            marginTop: 4,
            marginRight: 8,
            marginBottom: 4
          },
          legend: {
            size: 11,
            family: 'JetBrains Mono',
            weight: 'normal',
            color: '#a3a3a3',
            marginLeft: 8,
            marginTop: 4,
            marginRight: 8,
            marginBottom: 4,
            defaultValue: 'n/a'
          }
        }
      }
    });

    // Create volume indicator
    chart.createIndicator('VOL', true, { id: 'candle_pane' });
    
    // Create technical indicators
    chart.createIndicator('MA', false, { id: 'candle_pane' });
    chart.createIndicator('BOLL', false, { id: 'candle_pane' }); // Bollinger Bands
    chart.createIndicator('RSI', true);

    // Cleanup function
    return () => {
      if (chartInstanceRef.current) {
        dispose(chartRef.current!);
        chartInstanceRef.current = null;
      }
    };
  }, []);

  // Update chart data when symbol or timeframe changes
  useEffect(() => {
    if (chartInstanceRef.current && klineData.length > 0) {
      chartInstanceRef.current.applyNewData(klineData, true);
    }
  }, [klineData]);

  // Handle chart resize
  useEffect(() => {
    const handleResize = () => {
      if (chartInstanceRef.current) {
        chartInstanceRef.current.resize();
      }
    };

    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (!target.closest('.indicators-dropdown')) {
        setShowIndicators(false);
      }
    };

    window.addEventListener('resize', handleResize);
    document.addEventListener('mousedown', handleClickOutside);
    
    return () => {
      window.removeEventListener('resize', handleResize);
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className="h-full bg-trading-panel flex flex-col shadow-trading">
      {/* Chart Header */}
      <div className="p-trading border-b border-trading-border bg-trading-bg-darker flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <span className="font-medium text-trading-text font-mono">{selectedSymbol}</span>
            <span className="text-sm text-trading-text-muted">{currentPair?.name}</span>
            <ChevronDown className="h-4 w-4 text-trading-text-muted" />
          </div>
          
          {currentPair && (
            <div className="flex items-center space-x-4 text-sm">
              <div className="text-trading-text-muted">
                Bid: <span className="text-trading-down font-medium font-mono">{currentPair.bid.toFixed(5)}</span>
              </div>
              <div className="text-trading-text-muted">
                Ask: <span className="text-trading-up font-medium font-mono">{currentPair.ask.toFixed(5)}</span>
              </div>
              <div className={`font-medium font-mono ${currentPair.changePercent >= 0 ? 'text-trading-up' : 'text-trading-down'}`}>
                {currentPair.changePercent >= 0 ? '+' : ''}{currentPair.changePercent.toFixed(2)}%
              </div>
            </div>
          )}
        </div>

        <div className="flex items-center space-x-4">
          {/* Timeframe Buttons */}
          <div className="flex items-center space-x-1">
            {timeframes.map((tf) => (
              <button
                key={tf.id}
                onClick={() => setSelectedTimeframe(tf.id as any)}
                className={`px-2 py-1 text-xs font-medium rounded-trading transition-colors ${
                  selectedTimeframe === tf.id
                    ? 'bg-trading-accent-bg text-trading-accent border border-trading-accent/30'
                    : 'text-trading-text-muted hover:text-trading-text hover:bg-trading-border/20'
                }`}
              >
                {tf.label}
              </button>
            ))}
          </div>

          {/* Chart Tools */}
          <div className="flex items-center space-x-2">
            <button className="p-1.5 text-trading-text-muted hover:text-trading-text transition-colors rounded">
              <BarChart3 className="h-4 w-4" />
            </button>
            
            {/* Indicators Dropdown */}
            <div className="relative indicators-dropdown">
              <button 
                onClick={() => setShowIndicators(!showIndicators)}
                className="p-1.5 text-trading-text-muted hover:text-trading-text transition-colors rounded"
              >
                <TrendingUp className="h-4 w-4" />
              </button>
              
              {showIndicators && (
                <div className="absolute right-0 top-8 w-48 bg-trading-panel border border-trading-border rounded-trading shadow-trading-lg z-50">
                  <div className="p-2">
                    <div className="text-sm font-medium text-trading-text mb-2">Technical Indicators</div>
                    {indicators.map((indicator) => (
                      <button
                        key={indicator.id}
                        onClick={() => toggleIndicator(indicator.id)}
                        className="w-full text-left px-2 py-1 text-sm text-trading-text hover:bg-trading-border/20 rounded transition-colors flex items-center justify-between"
                      >
                        <span>{indicator.label}</span>
                        <div className={`w-2 h-2 rounded-full ${
                          indicator.enabled ? 'bg-trading-up' : 'bg-trading-text-dim'
                        }`} />
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>
            
            <button className="p-1.5 text-trading-text-muted hover:text-trading-text transition-colors rounded">
              <Settings className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Chart Container */}
      <div className="flex-1 relative bg-trading-bg-dark">
        <div 
          ref={chartRef} 
          className="absolute inset-0 w-full h-full"
          style={{ minHeight: '400px' }}
        />
        
        {/* Current Price Indicator */}
        {currentPair && (
          <div className="absolute right-4 top-1/2 -translate-y-1/2 z-10">
            <div className={`px-3 py-1.5 rounded-l text-xs font-bold text-white shadow-trading ${
              currentPair.changePercent >= 0 ? 'bg-trading-up' : 'bg-trading-down'
            }`}>
              <div className="text-center">
                <div className="leading-tight font-mono">{currentPair.ask.toFixed(currentPair.symbol.includes('JPY') ? 3 : 5)}</div>
                <div className="text-xs opacity-90">
                  {currentPair.changePercent >= 0 ? '↑' : '↓'} {Math.abs(currentPair.changePercent).toFixed(2)}%
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};